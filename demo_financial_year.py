"""
Demonstration of the new financial year functionality (MM-MM format).
"""
import json
from src.models import OrganizationalDetails

def demo_financial_year_format():
    """Demonstrate the new financial year format."""
    print("🎯 FINANCIAL YEAR FORMAT DEMONSTRATION")
    print("="*60)
    
    # Example power plants from different countries
    examples = [
        {
            "plant_name": "Kudankulam Nuclear Power Plant",
            "country": "India",
            "financial_year": "04-03",  # April to March
            "description": "Indian fiscal year (April 1 to March 31)"
        },
        {
            "plant_name": "Vogtle Nuclear Power Plant",
            "country": "United States", 
            "financial_year": "01-12",  # January to December
            "description": "US calendar year (January 1 to December 31)"
        },
        {
            "plant_name": "Hinkley Point C",
            "country": "United Kingdom",
            "financial_year": "04-03",  # April to March
            "description": "UK tax year (April 6 to April 5, simplified as 04-03)"
        },
        {
            "plant_name": "Loy Yang Power Station", 
            "country": "Australia",
            "financial_year": "07-06",  # July to June
            "description": "Australian financial year (July 1 to June 30)"
        },
        {
            "plant_name": "Gösgen Nuclear Power Plant",
            "country": "Switzerland",
            "financial_year": "01-12",  # January to December
            "description": "Swiss calendar year (January 1 to December 31)"
        }
    ]
    
    print("Examples of financial year formats by country:")
    print("-" * 60)
    
    for example in examples:
        print(f"\n🏭 {example['plant_name']}")
        print(f"   Country: {example['country']}")
        print(f"   Financial Year: {example['financial_year']}")
        print(f"   Description: {example['description']}")
        
        # Create and validate the model
        try:
            org_details = OrganizationalDetails(
                organization_name=f"Example Corp ({example['country']})",
                cfpp_type="nuclear" if "nuclear" in example['plant_name'].lower() else "coal",
                country_name=example['country'],
                financial_year=example['financial_year'],
                currency_in=get_currency_for_country(example['country'])
            )
            print(f"   ✅ Valid format")
        except Exception as e:
            print(f"   ❌ Invalid: {e}")
    
    print(f"\n{'='*60}")
    print("FORMAT SPECIFICATION")
    print(f"{'='*60}")
    print("Format: MM-MM (Start_Month-End_Month)")
    print("Examples:")
    print("  04-03 = April to March (India, UK, Japan)")
    print("  01-12 = January to December (USA, Germany, Switzerland)")
    print("  07-06 = July to June (Australia, New Zealand)")
    print("  10-09 = October to September (Some US Gov agencies)")

def get_currency_for_country(country):
    """Get currency code for a country."""
    currency_map = {
        "India": "INR",
        "United States": "USD",
        "United Kingdom": "GBP",
        "Australia": "AUD",
        "Switzerland": "CHF"
    }
    return currency_map.get(country, "USD")

def demo_json_output():
    """Show JSON output with new financial year format."""
    print(f"\n{'='*60}")
    print("JSON OUTPUT EXAMPLES")
    print(f"{'='*60}")
    
    # Create example outputs
    examples = [
        {
            "cfpp_type": "nuclear",
            "country_name": "India",
            "currency_in": "INR",
            "financial_year": "04-03",
            "organization_name": "Nuclear Power Corporation of India Limited",
            "plants_count": 22,
            "plant_types": ["nuclear"],
            "ppa_flag": True,
            "province": "Tamil Nadu"
        },
        {
            "cfpp_type": "nuclear", 
            "country_name": "United States",
            "currency_in": "USD",
            "financial_year": "01-12",
            "organization_name": "Georgia Power Company",
            "plants_count": 25,
            "plant_types": ["nuclear", "coal", "gas", "solar"],
            "ppa_flag": True,
            "province": "Georgia"
        },
        {
            "cfpp_type": "coal",
            "country_name": "Australia", 
            "currency_in": "AUD",
            "financial_year": "07-06",
            "organization_name": "AGL Energy Limited",
            "plants_count": 15,
            "plant_types": ["coal", "gas", "wind", "solar"],
            "ppa_flag": False,
            "province": "Victoria"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\nExample {i}: {example['country_name']}")
        print(json.dumps(example, indent=2))

def demo_validation():
    """Demonstrate validation of financial year format."""
    print(f"\n{'='*60}")
    print("VALIDATION EXAMPLES")
    print(f"{'='*60}")
    
    test_cases = [
        ("04-03", "✅ Valid - April to March"),
        ("01-12", "✅ Valid - January to December"),
        ("07-06", "✅ Valid - July to June"),
        ("13-01", "❌ Invalid - Month 13 doesn't exist"),
        ("01-13", "❌ Invalid - Month 13 doesn't exist"),
        ("4-3", "❌ Invalid - Must be zero-padded (04-03)"),
        ("2023", "❌ Invalid - Old year format"),
        ("", "✅ Valid - Empty string allowed"),
    ]
    
    print("Testing various financial year formats:")
    print("-" * 40)
    
    for test_value, expected in test_cases:
        try:
            org = OrganizationalDetails(financial_year=test_value)
            result = "✅ Valid"
        except Exception as e:
            result = "❌ Invalid"
        
        print(f"'{test_value}' -> {result} ({expected.split(' - ')[1] if ' - ' in expected else expected})")

def main():
    """Main demonstration function."""
    demo_financial_year_format()
    demo_json_output()
    demo_validation()
    
    print(f"\n{'='*60}")
    print("🎉 FINANCIAL YEAR FORMAT IMPLEMENTATION COMPLETE!")
    print(f"{'='*60}")
    print("Key Features:")
    print("✅ MM-MM format for international fiscal years")
    print("✅ Country-specific fiscal year patterns")
    print("✅ Automatic inference from country data")
    print("✅ Input validation and error handling")
    print("✅ Support for multiple input formats")
    print("\nReferences:")
    print("📖 https://en.wikipedia.org/wiki/Fiscal_year")
    print("📄 FINANCIAL_YEAR_FORMAT.md")

if __name__ == "__main__":
    main()
