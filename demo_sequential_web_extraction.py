"""
Demo script for sequential web-based data extraction.

This script demonstrates the sequential extraction workflow:
1. Organization level → Plant details → Unit level details
2. Uses web search only (no standard/mock values)
3. Provides sources for all scraped data
4. Uses cache memory first, then web search for missing fields
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from unittest.mock import MagicMock

# Mock problematic dependencies before importing
sys.modules['src.openai_client'] = MagicMock()

from src.sequential_web_extractor import SequentialWebExtractor
from src.config import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sequential_extraction.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class SequentialExtractionDemo:
    """Demo class for sequential web extraction."""

    def __init__(self):
        """Initialize the demo."""
        self.plant_name = "Jhajjar Power Plant"

        # Get API keys from environment or config
        self.serp_api_key = os.getenv('SERP_API_KEY') or config.pipeline.serp_api_key
        self.scraper_api_key = os.getenv('SCRAPER_API_KEY') or config.pipeline.scraper_api_key
        self.groq_api_key = os.getenv('GROQ_API_KEY') or config.pipeline.groq_api_key

        # Check if we have API keys for real extraction
        self.has_api_keys = all([self.serp_api_key, self.scraper_api_key, self.groq_api_key])

        if not self.has_api_keys:
            print("⚠️  API keys not found. Please check your .env file.")
            print("   Required: SERP_API_KEY, SCRAPER_API_KEY, GROQ_API_KEY")
            raise ValueError("Missing required API keys")

    async def run_sequential_extraction(self, force_refresh_all: bool = False):
        """Run the complete sequential extraction workflow."""
        print("🚀 Starting Sequential Web-Based Data Extraction")
        print("=" * 60)
        print(f"🏭 Plant: {self.plant_name}")
        print(f"📅 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔄 Force Refresh: {force_refresh_all}")
        print(f"🎭 Mode: REAL API EXTRACTION")
        print("=" * 60)

        try:
            # Initialize the sequential extractor
            extractor = SequentialWebExtractor(
                serp_api_key=self.serp_api_key,
                scraper_api_key=self.scraper_api_key,
                groq_api_key=self.groq_api_key
            )

            # Run sequential extraction
            start_time = datetime.now()

            org_details, plant_details, unit_details, extraction_info = await extractor.extract_sequential_data(
                plant_name=self.plant_name,
                force_refresh_org=force_refresh_all,
                force_refresh_plant=force_refresh_all,
                force_refresh_units=True  # Always refresh units to replace mock data
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # Display results summary
            self._display_extraction_summary(org_details, plant_details, unit_details, extraction_info, duration)

            # Save all results
            await extractor.save_results(
                org_details=org_details,
                plant_details=plant_details,
                unit_details=unit_details,
                extraction_info=extraction_info
            )

            print("\n✅ Sequential extraction completed successfully!")
            print(f"📁 Results saved to: sequential_extraction_results/")

            return org_details, plant_details, unit_details, extraction_info

        except Exception as e:
            logger.error(f"Sequential extraction failed: {e}")
            print(f"\n❌ Extraction failed: {e}")
            raise

    def _display_extraction_summary(
        self,
        org_details: dict,
        plant_details: dict,
        unit_details: dict,
        extraction_info: dict,
        duration: float
    ):
        """Display a summary of the extraction results."""
        print("\n📊 EXTRACTION SUMMARY")
        print("=" * 50)

        # Overall stats
        print(f"⏱️  Total Duration: {duration:.1f} seconds")
        print(f"🔍 Total Web Searches: {extraction_info['total_web_searches']}")
        print(f"📄 Total Pages Scraped: {extraction_info['total_pages_scraped']}")
        print(f"📋 Phases Completed: {', '.join(extraction_info['phases_completed'])}")

        # Cache usage
        cache_usage = extraction_info['cache_usage']
        print(f"\n💾 Cache Usage:")
        print(f"   📊 Org from cache: {'✅' if cache_usage['org_from_cache'] else '❌'}")
        print(f"   🏭 Plant from cache: {'✅' if cache_usage['plant_from_cache'] else '❌'}")
        print(f"   ⚡ Units from cache: {'✅' if cache_usage['units_from_cache'] else '❌'}")

        # Organizational details
        org_filled = sum(1 for v in org_details.values() if v not in [None, "", []])
        print(f"\n📊 Organizational Details: {org_filled}/{len(org_details)} fields")
        for field, value in org_details.items():
            if value not in [None, "", []]:
                print(f"   ✅ {field}: {value}")
            else:
                print(f"   ❌ {field}: (empty)")

        # Plant details
        plant_filled = sum(1 for v in plant_details.values() if v not in [None, "", []])
        print(f"\n🏭 Plant Details: {plant_filled}/{len(plant_details)} fields")
        key_plant_fields = ["name", "plant_type", "lat", "long", "plant_address"]
        for field in key_plant_fields:
            value = plant_details.get(field, "")
            if value not in [None, "", []]:
                print(f"   ✅ {field}: {value}")
            else:
                print(f"   ❌ {field}: (empty)")

        # Unit details
        units = unit_details.get("units", [])
        print(f"\n⚡ Unit Details: {len(units)} units extracted")
        for i, unit in enumerate(units):
            unit_filled = sum(1 for v in unit.values() if v not in [None, "", []])
            print(f"   🔧 Unit {i+1}: {unit_filled} fields extracted")

        # Source tracking summary
        source_summary = extraction_info.get('source_summary', {})
        if source_summary:
            stats = source_summary.get('source_statistics', {})
            print(f"\n🔍 Source Tracking:")
            print(f"   📋 Cached fields: {stats.get('cached_fields', 0)}")
            print(f"   🌐 Web searches: {stats.get('web_searches', 0)}")
            print(f"   📄 Scraped pages: {stats.get('scraped_pages', 0)}")
            print(f"   🧠 LLM extractions: {stats.get('llm_extractions', 0)}")


async def main():
    """Main function to run the demo."""
    demo = SequentialExtractionDemo()

    print("🌐 Sequential Web-Based Data Extraction Demo")
    print("This demo will extract data in sequence: Org → Plant → Units")
    print("All data will be sourced from web searches with full source tracking")
    print()

    # Ask user for refresh preference
    refresh_choice = input("Force refresh all cached data? (y/N): ").strip().lower()
    force_refresh = refresh_choice in ['y', 'yes']

    try:
        await demo.run_sequential_extraction(force_refresh_all=force_refresh)

    except KeyboardInterrupt:
        print("\n⚠️ Extraction interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.exception("Demo execution failed")


if __name__ == "__main__":
    asyncio.run(main())
