#!/usr/bin/env python3
"""
Unit Details Schema Filling Demo
Demonstrates filling the specific unit_details.json schema using cached plant data and targeted searches.
"""
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class UnitSchemaFillingDemo:
    """Demo for filling unit details schema."""
    
    def __init__(self):
        """Initialize the demo."""
        self.plant_name = "Jhajjar Power Plant"
        
    async def run_unit_schema_filling_demo(self):
        """Run the complete unit schema filling demo."""
        print("📋 UNIT DETAILS SCHEMA FILLING DEMO")
        print("=" * 70)
        print(f"🔍 Target Plant: {self.plant_name}")
        print(f"📊 Method: Fill unit_details.json schema using cached data + targeted searches")
        print(f"💾 Features: Cache utilization + Google searches + Comprehensive field mapping")
        print("=" * 70)
        
        start_time = time.time()
        
        # Step 1: Display the original schema
        print("\n📋 STEP 1: ANALYZING UNIT DETAILS SCHEMA")
        print("-" * 50)
        await self._analyze_schema()
        
        # Step 2: Ensure cached plant data is available
        print("\n💾 STEP 2: ENSURING CACHED PLANT DATA AVAILABILITY")
        print("-" * 50)
        await self._ensure_cached_data()
        
        # Step 3: Fill schema for Unit 1
        print("\n🔧 STEP 3: FILLING SCHEMA FOR UNIT 1")
        print("-" * 50)
        unit1_details = await self._fill_unit_schema(1)
        
        # Step 4: Fill schema for Unit 2
        print("\n🔧 STEP 4: FILLING SCHEMA FOR UNIT 2")
        print("-" * 50)
        unit2_details = await self._fill_unit_schema(2)
        
        # Step 5: Display filled data summary
        print("\n📊 STEP 5: FILLED DATA SUMMARY")
        print("-" * 50)
        self._display_filled_summary(unit1_details, unit2_details)
        
        # Step 6: Save filled schemas
        print("\n💾 STEP 6: SAVING FILLED SCHEMAS")
        print("-" * 50)
        await self._save_filled_schemas(unit1_details, unit2_details)
        
        total_time = time.time() - start_time
        print(f"\n🎉 UNIT SCHEMA FILLING DEMO COMPLETED!")
        print(f"⏱️  Total time: {total_time:.1f} seconds")
        print(f"📋 Units processed: 2")
        
        return unit1_details, unit2_details
    
    async def _analyze_schema(self):
        """Analyze the unit details schema."""
        try:
            with open("unit_details.json", 'r', encoding='utf-8') as f:
                schema = json.load(f)
            
            print("📋 Unit details schema analysis:")
            print(f"   • Total fields in schema: {len(schema)}")
            
            # Categorize fields
            basic_fields = ["unit_number", "plant_id", "capacity", "technology", "fuel_type"]
            performance_fields = ["heat_rate", "unit_efficiency", "plf", "PAF", "auxiliary_power_consumed"]
            environmental_fields = ["emission_factor", "gcv_coal", "gcv_natural_gas", "gcv_biomass"]
            operational_fields = ["commencement_date", "remaining_useful_life", "unit_lifetime"]
            economic_fields = ["capex_required_renovation_closed_cycle", "capex_required_renovation_open_cycle"]
            
            print(f"   • Basic info fields: {len(basic_fields)}")
            print(f"   • Performance fields: {len(performance_fields)}")
            print(f"   • Environmental fields: {len(environmental_fields)}")
            print(f"   • Operational fields: {len(operational_fields)}")
            print(f"   • Economic fields: {len(economic_fields)}")
            
            # Show some key field descriptions
            print(f"\n   📝 Key field descriptions:")
            key_fields = ["capacity", "heat_rate", "unit_efficiency", "emission_factor", "ppa_details"]
            for field in key_fields:
                if field in schema:
                    desc = schema[field]
                    if isinstance(desc, str):
                        print(f"      • {field}: {desc[:80]}...")
                    elif isinstance(desc, list) and desc:
                        print(f"      • {field}: {desc[0].get('value', str(desc[0]))[:80]}...")
            
        except FileNotFoundError:
            print("❌ unit_details.json schema file not found!")
            raise
    
    async def _ensure_cached_data(self):
        """Ensure cached plant data is available."""
        from src.cache_manager import plant_cache
        
        # Check if cached data exists
        cached_data = plant_cache.get_plant_details(self.plant_name)
        
        if cached_data:
            print(f"✅ Cached plant data found with {len(cached_data)} fields")
            print(f"   • Plant type: {cached_data.get('plant_type', 'N/A')}")
            print(f"   • Units: {cached_data.get('units_id', [])}")
            print(f"   • PPA details: {len(cached_data.get('ppa_details', []))} agreements")
        else:
            print("⚠️  No cached data found. Creating mock cached data...")
            
            # Create mock plant data for demo
            mock_plant_data = {
                "name": "Jhajjar Power Plant",
                "plant_type": "coal",
                "lat": "28.607111",
                "long": "76.656914",
                "plant_address": "Jharli village, Jhajjar district, Haryana, India",
                "units_id": [1, 2],
                "plant_id": 1,
                "ppa_details": [
                    {
                        "capacity": "1320 MW",
                        "capacity_unit": "MW",
                        "start_date": "2012",
                        "end_date": "",
                        "tenure": 30,
                        "tenure_type": "Years",
                        "respondents": [
                            {
                                "name": "Haryana State Distribution Companies",
                                "capacity": "1188 MW",
                                "currency": "INR",
                                "price": "2.89",
                                "price_unit": "INR/kWh"
                            },
                            {
                                "name": "External Power Buyers",
                                "capacity": "132 MW",
                                "currency": "INR",
                                "price": "3.15",
                                "price_unit": "INR/kWh"
                            }
                        ]
                    }
                ]
            }
            
            plant_cache.store_plant_details(self.plant_name, mock_plant_data)
            print("✅ Mock cached data created and stored")
    
    async def _fill_unit_schema(self, unit_number: int) -> Dict[str, Any]:
        """Fill schema for a specific unit."""
        from src.unit_details_schema_filler import UnitDetailsSchemaFiller
        
        print(f"🔧 Initializing schema filler for Unit {unit_number}...")
        filler = UnitDetailsSchemaFiller()
        
        print(f"📋 Filling unit details schema for Unit {unit_number}...")
        print("💾 Using cached plant data as foundation...")
        print("🔍 Performing targeted searches for missing fields...")
        
        # Fill the schema
        filled_details = await filler.fill_unit_details_schema(
            self.plant_name, unit_number, use_cached_data=True
        )
        
        # Count filled fields
        filled_count = sum(1 for v in filled_details.values() if v not in [None, "", []])
        
        print(f"✅ Schema filling completed for Unit {unit_number}")
        print(f"📊 Fields filled: {filled_count}")
        
        return filled_details
    
    def _display_filled_summary(self, unit1_details: Dict[str, Any], unit2_details: Dict[str, Any]):
        """Display summary of filled data."""
        print("📊 Filled data summary:")
        
        # Display key fields for both units
        key_fields = [
            "unit_number", "capacity", "technology", "heat_rate", 
            "unit_efficiency", "boiler_type", "commencement_date"
        ]
        
        print(f"\n   🔧 Unit 1 Key Fields:")
        for field in key_fields:
            value = unit1_details.get(field, "N/A")
            if isinstance(value, list) and value:
                value = f"{len(value)} entries"
            print(f"      • {field}: {value}")
        
        print(f"\n   🔧 Unit 2 Key Fields:")
        for field in key_fields:
            value = unit2_details.get(field, "N/A")
            if isinstance(value, list) and value:
                value = f"{len(value)} entries"
            print(f"      • {field}: {value}")
        
        # Show performance metrics
        print(f"\n   📈 Performance Metrics:")
        for unit_num, unit_data in [(1, unit1_details), (2, unit2_details)]:
            plf_data = unit_data.get("plf", [])
            paf_data = unit_data.get("PAF", [])
            if plf_data:
                latest_plf = plf_data[0].get("value", "N/A") if plf_data else "N/A"
                print(f"      • Unit {unit_num} PLF: {latest_plf}%")
            if paf_data:
                latest_paf = paf_data[0].get("value", "N/A") if paf_data else "N/A"
                print(f"      • Unit {unit_num} PAF: {latest_paf}%")
        
        # Show environmental data
        print(f"\n   🌱 Environmental Data:")
        for unit_num, unit_data in [(1, unit1_details), (2, unit2_details)]:
            emission_data = unit_data.get("emission_factor", [])
            if emission_data:
                latest_emission = emission_data[0].get("value", "N/A") if emission_data else "N/A"
                print(f"      • Unit {unit_num} CO2 Emissions: {latest_emission} kg CO2e/kWh")
        
        # Show PPA information
        print(f"\n   📄 PPA Information:")
        for unit_num, unit_data in [(1, unit1_details), (2, unit2_details)]:
            ppa_data = unit_data.get("ppa_details", [])
            if ppa_data:
                unit_capacity = ppa_data[0].get("capacity", "N/A")
                respondents_count = len(ppa_data[0].get("respondents", []))
                print(f"      • Unit {unit_num} PPA Capacity: {unit_capacity} MW")
                print(f"      • Unit {unit_num} Respondents: {respondents_count}")
    
    async def _save_filled_schemas(self, unit1_details: Dict[str, Any], unit2_details: Dict[str, Any]):
        """Save filled schemas to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save individual unit schemas
        unit1_file = f"unit_1_schema_filled_{timestamp}.json"
        with open(unit1_file, 'w', encoding='utf-8') as f:
            json.dump(unit1_details, f, indent=2, ensure_ascii=False)
        print(f"📋 Unit 1 filled schema saved to: {unit1_file}")
        
        unit2_file = f"unit_2_schema_filled_{timestamp}.json"
        with open(unit2_file, 'w', encoding='utf-8') as f:
            json.dump(unit2_details, f, indent=2, ensure_ascii=False)
        print(f"📋 Unit 2 filled schema saved to: {unit2_file}")
        
        # Save combined schema
        combined_file = f"all_units_schema_filled_{timestamp}.json"
        combined_data = {
            "plant_name": self.plant_name,
            "extraction_timestamp": datetime.now().isoformat(),
            "schema_version": "1.0",
            "total_units": 2,
            "units": {
                "unit_1": unit1_details,
                "unit_2": unit2_details
            }
        }
        
        with open(combined_file, 'w', encoding='utf-8') as f:
            json.dump(combined_data, f, indent=2, ensure_ascii=False)
        print(f"📋 Combined filled schema saved to: {combined_file}")


async def main():
    """Main demo function."""
    print("📋 UNIT DETAILS SCHEMA FILLING DEMO")
    print("This demo fills the specific unit_details.json schema using cached data and searches")
    print()
    
    try:
        demo = UnitSchemaFillingDemo()
        unit1_details, unit2_details = await demo.run_unit_schema_filling_demo()
        
        print(f"\n📄 SAMPLE FILLED SCHEMA PREVIEW (Unit 1)")
        print("=" * 60)
        
        # Show a subset of the filled data
        preview_fields = ["unit_number", "capacity", "technology", "heat_rate", "unit_efficiency", "ppa_details"]
        preview_data = {field: unit1_details.get(field, "N/A") for field in preview_fields}
        print(json.dumps(preview_data, indent=2, ensure_ascii=False))
        
        print(f"\n✅ UNIT SCHEMA FILLING DEMO COMPLETED SUCCESSFULLY!")
        print("Check the generated JSON files for complete filled schemas.")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        logger.error(f"Demo failed: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
