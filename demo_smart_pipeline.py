"""
Demo script for the smart unified pipeline with intelligent caching.
"""
import asyncio
import logging
import json
import sys
from datetime import datetime

from src.smart_unified_pipeline import SmartUnifiedPowerPlantPipeline


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'smart_pipeline_demo_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            logging.StreamHandler()
        ]
    )


async def demo_smart_extraction():
    """Demo the smart unified pipeline with caching optimization."""
    
    print("🧠 Smart Unified Pipeline Demo with Intelligent Caching")
    print("=" * 70)
    
    # Test plants
    test_plants = [
        "Vogtle Nuclear Power Plant",
        "Palo Verde Nuclear Generating Station", 
        "Hoover Dam",
        "Ivanpah Solar Power Facility"
    ]
    
    # Get plant selection
    if len(sys.argv) > 1:
        plant_name = " ".join(sys.argv[1:])
    else:
        print("\nAvailable test plants:")
        for i, plant in enumerate(test_plants, 1):
            print(f"{i}. {plant}")
        
        choice = input("\nEnter plant number (1-4) or custom name: ").strip()
        
        if choice.isdigit() and 1 <= int(choice) <= len(test_plants):
            plant_name = test_plants[int(choice) - 1]
        else:
            plant_name = choice if choice else test_plants[0]
    
    # Initialize smart pipeline
    try:
        pipeline = SmartUnifiedPowerPlantPipeline()
        print(f"\n✅ Smart pipeline initialized successfully")
    except Exception as e:
        print(f"\n❌ Failed to initialize pipeline: {e}")
        return
    
    print(f"\n🔍 Starting smart extraction for: {plant_name}")
    print("=" * 50)
    
    # Demo different extraction modes
    modes = [
        ("Smart Cache-Optimized", True, True, True),
        ("Traditional Full Search", True, True, False),
        ("Organizational Only", True, False, True),
        ("Technical Only", False, True, True)
    ]
    
    mode_choice = input("\nChoose extraction mode:\n"
                       "1. Smart Cache-Optimized (Recommended)\n"
                       "2. Traditional Full Search\n"
                       "3. Organizational Only\n"
                       "4. Technical Only\n"
                       "Enter choice (1-4): ").strip()
    
    try:
        mode_index = int(mode_choice) - 1 if mode_choice.isdigit() else 0
        mode_name, extract_org, extract_tech, use_cache = modes[mode_index]
    except (ValueError, IndexError):
        mode_name, extract_org, extract_tech, use_cache = modes[0]  # Default to smart mode
    
    print(f"\n🚀 Running: {mode_name}")
    print("-" * 30)
    
    try:
        start_time = datetime.now()
        
        # Extract data using smart pipeline
        org_details, plant_details, cache_info = await pipeline.extract_complete_plant_data_smart(
            plant_name,
            extract_organizational=extract_org,
            extract_technical=extract_tech,
            use_cache_optimization=use_cache
        )
        
        end_time = datetime.now()
        total_duration = (end_time - start_time).total_seconds()
        
        print(f"\n⏱️  Total extraction time: {total_duration:.1f} seconds")
        
        # Display cache efficiency metrics
        if cache_info:
            print(f"\n📊 CACHE EFFICIENCY METRICS")
            print("-" * 40)
            print(f"🔍 Organizational search time: {cache_info.get('organizational_search_time', 0):.1f}s")
            print(f"🔧 Plant search time: {cache_info.get('plant_search_time', 0):.1f}s")
            print(f"💾 Cache hit fields: {len(cache_info.get('cache_hit_fields', []))}")
            print(f"🔎 Additional searches: {len(cache_info.get('additional_searches', []))}")
            print(f"📞 Estimated API calls: {cache_info.get('total_api_calls', 0)}")
            
            if cache_info.get('cache_hit_fields'):
                print(f"✅ Fields extracted from cache: {', '.join(cache_info['cache_hit_fields'][:5])}{'...' if len(cache_info['cache_hit_fields']) > 5 else ''}")
            
            if cache_info.get('additional_searches'):
                print(f"🔍 Additional search categories: {', '.join(cache_info['additional_searches'])}")
        
        # Save results
        safe_name = plant_name.lower().replace(" ", "_").replace("-", "_")
        org_file = f"org_details_{safe_name}_smart.json"
        plant_file = f"plant_details_{safe_name}_smart.json"
        cache_file = f"cache_analysis_{safe_name}.json"
        
        await pipeline.save_results(
            org_details=org_details,
            plant_details=plant_details,
            cache_info=cache_info,
            org_output_path=org_file,
            plant_output_path=plant_file,
            cache_output_path=cache_file
        )
        
        # Display extraction summary
        print(f"\n📋 EXTRACTION SUMMARY")
        print("-" * 30)
        
        if org_details:
            org_data = org_details.model_dump()
            filled_org_fields = sum(1 for v in org_data.values() if v not in [None, "", []])
            print(f"📊 Organizational: {filled_org_fields}/{len(org_data)} fields")
            
            if org_data.get('organization_name'):
                print(f"   🏢 Organization: {org_data['organization_name']}")
            if org_data.get('country_name'):
                print(f"   🌍 Country: {org_data['country_name']}")
        
        if plant_details:
            plant_data = plant_details.model_dump()
            filled_plant_fields = sum(1 for v in plant_data.values() if v not in [None, "", []])
            print(f"🔧 Plant Technical: {filled_plant_fields}/{len(plant_data)} fields")
            
            if plant_data.get('plant_type'):
                print(f"   ⚙️  Type: {plant_data['plant_type']}")
            if plant_data.get('lat') and plant_data.get('long'):
                print(f"   📍 Coordinates: {plant_data['lat']}, {plant_data['long']}")
            if plant_data.get('units_id'):
                print(f"   🔢 Units: {len(plant_data['units_id'])}")
        
        print(f"\n💾 Results saved to: {org_file}, {plant_file}, {cache_file}")
        
        # Calculate efficiency gains
        if use_cache and cache_info:
            cache_hits = len(cache_info.get('cache_hit_fields', []))
            additional_searches = len(cache_info.get('additional_searches', []))
            total_possible_searches = 10  # Rough estimate of full search categories
            
            efficiency = ((total_possible_searches - additional_searches) / total_possible_searches) * 100
            print(f"\n🎯 EFFICIENCY GAINS")
            print(f"   💡 Cache efficiency: {efficiency:.1f}%")
            print(f"   ⚡ Avoided searches: {total_possible_searches - additional_searches}/{total_possible_searches}")
            
            if efficiency > 50:
                print(f"   🏆 Excellent cache utilization!")
            elif efficiency > 25:
                print(f"   👍 Good cache utilization")
            else:
                print(f"   📝 Limited cache benefit for this plant")
        
        # Option to show detailed results
        show_details = input("\nShow detailed extraction results? (y/n): ").strip().lower()
        if show_details == 'y':
            if org_details:
                print(f"\n📊 DETAILED ORGANIZATIONAL RESULTS:")
                print("-" * 50)
                print(json.dumps(org_details.model_dump(), indent=2))
            
            if plant_details:
                print(f"\n🔧 DETAILED PLANT TECHNICAL RESULTS:")
                print("-" * 50)
                print(json.dumps(plant_details.model_dump(), indent=2))
            
            if cache_info:
                print(f"\n💾 DETAILED CACHE ANALYSIS:")
                print("-" * 50)
                print(json.dumps(cache_info, indent=2))
    
    except Exception as e:
        print(f"\n❌ Smart extraction failed: {e}")
        logging.error(f"Smart extraction failed for {plant_name}: {e}", exc_info=True)


async def demo_cache_comparison():
    """Demo comparing cache-optimized vs traditional extraction."""
    
    print("🔄 Cache Optimization Comparison Demo")
    print("=" * 50)
    
    plant_name = input("Enter plant name for comparison: ").strip()
    if not plant_name:
        plant_name = "Vogtle Nuclear Power Plant"
    
    pipeline = SmartUnifiedPowerPlantPipeline()
    
    print(f"\n🧪 Comparing extraction methods for: {plant_name}")
    
    # Method 1: Smart cache-optimized
    print(f"\n1️⃣  Smart Cache-Optimized Extraction")
    print("-" * 40)
    start_time = datetime.now()
    
    org1, plant1, cache1 = await pipeline.extract_complete_plant_data_smart(
        plant_name, use_cache_optimization=True
    )
    
    smart_time = (datetime.now() - start_time).total_seconds()
    
    # Method 2: Traditional full search
    print(f"\n2️⃣  Traditional Full Search")
    print("-" * 40)
    start_time = datetime.now()
    
    org2, plant2, cache2 = await pipeline.extract_complete_plant_data_smart(
        plant_name, use_cache_optimization=False
    )
    
    traditional_time = (datetime.now() - start_time).total_seconds()
    
    # Comparison results
    print(f"\n📊 COMPARISON RESULTS")
    print("=" * 40)
    print(f"⏱️  Smart method time: {smart_time:.1f}s")
    print(f"⏱️  Traditional method time: {traditional_time:.1f}s")
    print(f"🚀 Speed improvement: {((traditional_time - smart_time) / traditional_time * 100):.1f}%")
    
    if cache1:
        print(f"💾 Cache hits: {len(cache1.get('cache_hit_fields', []))}")
        print(f"🔍 Additional searches: {len(cache1.get('additional_searches', []))}")
    
    print(f"\n🎉 Smart caching {'saved' if smart_time < traditional_time else 'used'} "
          f"{abs(traditional_time - smart_time):.1f} seconds!")


async def main():
    """Main demo function."""
    setup_logging()
    
    print("Smart Unified Pipeline Demo")
    print("Choose demo mode:")
    print("1. Smart extraction demo")
    print("2. Cache comparison demo")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "2":
        await demo_cache_comparison()
    else:
        await demo_smart_extraction()


if __name__ == "__main__":
    asyncio.run(main())
