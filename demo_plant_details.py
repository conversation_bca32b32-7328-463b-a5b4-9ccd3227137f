"""
Demo script for testing plant details extraction pipeline.
"""
import asyncio
import logging
import json
import sys
from datetime import datetime

from src.unified_pipeline import UnifiedPowerPlantPipeline


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'plant_details_demo_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            logging.StreamHandler()
        ]
    )


async def demo_plant_details_extraction():
    """Demo the plant details extraction pipeline."""
    
    # Test plants with different characteristics
    test_plants = [
        "Vogtle Nuclear Power Plant",
        "Palo Verde Nuclear Generating Station", 
        "Hoover Dam",
        "Ivanpah Solar Power Facility",
        "Block Island Wind Farm"
    ]
    
    print("🚀 Plant Details Extraction Pipeline Demo")
    print("=" * 60)
    
    # Get plant name from command line or use interactive selection
    if len(sys.argv) > 1:
        plant_name = " ".join(sys.argv[1:])
        selected_plants = [plant_name]
    else:
        print("\nAvailable test plants:")
        for i, plant in enumerate(test_plants, 1):
            print(f"{i}. {plant}")
        
        print("\nOptions:")
        print("- Enter a number (1-5) to test a specific plant")
        print("- Enter 'all' to test all plants")
        print("- Enter a custom plant name")
        
        choice = input("\nYour choice: ").strip()
        
        if choice.lower() == 'all':
            selected_plants = test_plants
        elif choice.isdigit() and 1 <= int(choice) <= len(test_plants):
            selected_plants = [test_plants[int(choice) - 1]]
        else:
            selected_plants = [choice]
    
    # Initialize pipeline
    try:
        pipeline = UnifiedPowerPlantPipeline()
        print(f"\n✅ Pipeline initialized successfully")
    except Exception as e:
        print(f"\n❌ Failed to initialize pipeline: {e}")
        return
    
    # Process each selected plant
    for i, plant_name in enumerate(selected_plants, 1):
        print(f"\n{'='*60}")
        print(f"Processing Plant {i}/{len(selected_plants)}: {plant_name}")
        print(f"{'='*60}")
        
        try:
            # Extract data
            print(f"\n🔍 Starting extraction for: {plant_name}")
            start_time = datetime.now()
            
            org_details, plant_details = await pipeline.extract_complete_plant_data(
                plant_name,
                extract_organizational=True,
                extract_technical=True
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            print(f"⏱️  Extraction completed in {duration:.1f} seconds")
            
            # Save results with plant-specific filenames
            safe_name = plant_name.lower().replace(" ", "_").replace("-", "_")
            org_file = f"org_details_{safe_name}.json"
            plant_file = f"plant_details_{safe_name}.json"
            
            await pipeline.save_results(
                org_details, 
                plant_details,
                org_output_path=org_file,
                plant_output_path=plant_file
            )
            
            # Display results summary
            print(f"\n📊 EXTRACTION SUMMARY FOR: {plant_name}")
            print("-" * 50)
            
            if org_details:
                org_data = org_details.model_dump()
                filled_org_fields = sum(1 for v in org_data.values() if v not in [None, "", []])
                print(f"📋 Organizational Details: {filled_org_fields}/{len(org_data)} fields extracted")
                
                # Show key organizational info
                if org_data.get('organization_name'):
                    print(f"   🏢 Organization: {org_data['organization_name']}")
                if org_data.get('country_name'):
                    print(f"   🌍 Country: {org_data['country_name']}")
                if org_data.get('plant_types'):
                    print(f"   ⚡ Plant Types: {', '.join(org_data['plant_types'])}")
            
            if plant_details:
                plant_data = plant_details.model_dump()
                filled_plant_fields = sum(1 for v in plant_data.values() if v not in [None, "", []])
                print(f"🔧 Plant Technical Details: {filled_plant_fields}/{len(plant_data)} fields extracted")
                
                # Show key technical info
                if plant_data.get('plant_type'):
                    print(f"   ⚙️  Plant Type: {plant_data['plant_type']}")
                if plant_data.get('lat') and plant_data.get('long'):
                    print(f"   📍 Coordinates: {plant_data['lat']}, {plant_data['long']}")
                if plant_data.get('units_id'):
                    print(f"   🔢 Units: {len(plant_data['units_id'])} identified")
                if plant_data.get('ppa_details'):
                    print(f"   📄 PPAs: {len(plant_data['ppa_details'])} agreements found")
            
            print(f"💾 Results saved to: {org_file}, {plant_file}")
            
            # Option to display full results
            if len(selected_plants) == 1:
                show_full = input("\nShow full extraction results? (y/n): ").strip().lower()
                if show_full == 'y':
                    print(f"\n📋 FULL ORGANIZATIONAL DETAILS:")
                    print("-" * 40)
                    if org_details:
                        print(json.dumps(org_details.model_dump(), indent=2))
                    else:
                        print("No organizational details extracted")
                    
                    print(f"\n🔧 FULL PLANT TECHNICAL DETAILS:")
                    print("-" * 40)
                    if plant_details:
                        print(json.dumps(plant_details.model_dump(), indent=2))
                    else:
                        print("No plant details extracted")
        
        except Exception as e:
            print(f"\n❌ Extraction failed for {plant_name}: {e}")
            logging.error(f"Extraction failed for {plant_name}: {e}", exc_info=True)
    
    print(f"\n🎉 Demo completed! Check the generated JSON files for detailed results.")


async def demo_technical_only():
    """Demo technical details extraction only."""
    print("🔧 Technical Details Only Demo")
    print("=" * 40)
    
    plant_name = input("Enter plant name: ").strip()
    if not plant_name:
        plant_name = "Vogtle Nuclear Power Plant"
    
    try:
        pipeline = UnifiedPowerPlantPipeline()
        
        print(f"\n🔍 Extracting technical details for: {plant_name}")
        
        org_details, plant_details = await pipeline.extract_complete_plant_data(
            plant_name,
            extract_organizational=False,
            extract_technical=True
        )
        
        if plant_details:
            print(f"\n✅ Technical extraction successful!")
            print(json.dumps(plant_details.model_dump(), indent=2))
        else:
            print(f"\n❌ No technical details extracted")
    
    except Exception as e:
        print(f"\n❌ Technical extraction failed: {e}")


async def main():
    """Main demo function."""
    setup_logging()
    
    print("Plant Details Extraction Demo")
    print("Choose demo mode:")
    print("1. Full extraction (organizational + technical)")
    print("2. Technical details only")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "2":
        await demo_technical_only()
    else:
        await demo_plant_details_extraction()


if __name__ == "__main__":
    asyncio.run(main())
