"""
Schema-Compliant Groq Client for power plant data extraction.
Applies the same successful schema-compliance strategy from OpenAI to Groq.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
import groq
from groq import AsyncGroq

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GroqExtractionResult:
    """Result of Groq field extraction with confidence score."""
    extracted_value: Any
    confidence_score: float
    source_info: str = ""
    extraction_method: str = "groq_schema_compliant"
    retry_count: int = 0

class SchemaCompliantGroqClient:
    """
    Schema-compliant Groq client that ensures 100% schema adherence.
    Applies the same successful strategy from OpenAI to Groq with rate limit handling.
    """
    
    def __init__(self, api_key: str, model: str = "llama-3.3-70b-versatile"):
        """
        Initialize schema-compliant Groq extraction client.
        
        Args:
            api_key: Groq API key
            model: Groq model to use
        """
        self.client = AsyncGroq(api_key=api_key)
        self.model = model
        self.extraction_count = 0
        self.total_tokens_used = 0
        self.rate_limit_delays = 0
        self.max_retries = 3
        self.base_delay = 1.0  # Base delay for rate limiting
        
        logger.info(f"Schema-compliant Groq client initialized with model: {model}")

    async def extract_field_with_schema_compliance(self, field_name: str, content: str, context: str = "") -> Optional[GroqExtractionResult]:
        """
        Extract field with guaranteed schema compliance and rate limit handling.
        
        Args:
            field_name: Name of the field to extract
            content: Text content to extract from
            context: Additional context for extraction
            
        Returns:
            GroqExtractionResult with extracted value and metadata
        """
        for attempt in range(self.max_retries):
            try:
                self.extraction_count += 1
                
                # Create schema-enforced prompt
                prompt = self._create_schema_compliant_prompt(field_name, content, context)
                
                # Add rate limiting delay
                if attempt > 0:
                    delay = self.base_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"Rate limit retry {attempt}, waiting {delay}s for {field_name}")
                    await asyncio.sleep(delay)
                    self.rate_limit_delays += 1
                
                # Call Groq API with schema enforcement
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert power plant data extraction specialist. You MUST return valid JSON that exactly matches the requested schema. Never return partial or malformed JSON."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=800,
                    response_format={"type": "json_object"}
                )
                
                # Track token usage if available
                if hasattr(response, 'usage') and response.usage:
                    self.total_tokens_used += response.usage.total_tokens
                
                # Parse and validate response
                result_text = response.choices[0].message.content
                result_json = json.loads(result_text)
                
                # Extract value and confidence with schema validation
                extracted_value = result_json.get("value")
                confidence = float(result_json.get("confidence", 0.0))
                reasoning = result_json.get("reasoning", "")
                
                # Apply schema-specific post-processing
                processed_value = self._apply_schema_post_processing(field_name, extracted_value)
                
                logger.info(f"Groq extracted {field_name}: {str(processed_value)[:50]}{'...' if len(str(processed_value)) > 50 else ''} (confidence: {confidence:.2f}, attempt: {attempt + 1})")
                
                return GroqExtractionResult(
                    extracted_value=processed_value,
                    confidence_score=confidence,
                    source_info=reasoning,
                    extraction_method=f"groq_schema_compliant_{self.model}",
                    retry_count=attempt
                )
                
            except groq.RateLimitError as e:
                logger.warning(f"Groq rate limit hit for {field_name} (attempt {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"Max retries exceeded for {field_name}")
                    return None
                continue
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing failed for Groq extraction {field_name} (attempt {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    return None
                continue
                
            except Exception as e:
                logger.error(f"Groq extraction failed for field {field_name} (attempt {attempt + 1}): {e}")
                if attempt == self.max_retries - 1:
                    return None
                continue
        
        return None

    def _create_schema_compliant_prompt(self, field_name: str, content: str, context: str) -> str:
        """Create schema-compliant prompt for Groq extraction."""
        
        # Field-specific schema instructions
        schema_instructions = {
            "cfpp_type": "Return the ownership type of the power plant company (e.g., 'private', 'public', 'joint-venture', 'government', 'state-owned')",
            "country_name": "Return the country name as a string",
            "currency_in": "Return the currency code (e.g., 'INR', 'USD', 'EUR')",
            "financial_year": "Return the fiscal year period format for the country where plant is located (e.g., '04-03' for India, '01-12' for USA, '04-03' for UK, '07-06' for Australia)",
            "organization_name": "Return the organization/company name as a string",
            "plants_count": "Return the number of plants as an integer",
            "plant_types": "Return an array of plant types (e.g., ['coal', 'gas'])",
            "ppa_flag": "Return PPA flag as string (e.g., 'Plant', 'Organization')",
            "province": "Return the state/province name as a string",
            "capacity": "Return unit capacity as a number (e.g., 660)",
            "capacity_unit": "Return capacity unit as string (e.g., 'MW')",
            "technology": "Return technology type as string (e.g., 'supercritical', 'subcritical')",
            "fuel_type": "Return fuel type as string or array",
            "heat_rate": "Return heat rate as a number",
            "heat_rate_unit": "Return heat rate unit as string (e.g., 'kcal/kWh')",
            "commencement_date": "Return date in ISO format (e.g., '2012-01-01')",
            "boiler_type": "Return boiler type as string",
            "unit_efficiency": "Return efficiency as a number (percentage)",
            "lat": "Return latitude as a number",
            "long": "Return longitude as a number",
            "name": "Return plant name as a string",
            "plant_address": "Return full address as a string",
            "plant_type": "Return plant type as a string",
            "ppa_details": "Return PPA details as string or array",
            "units_id": "Return array of unit IDs (e.g., ['1', '2'])"
        }
        
        instruction = schema_instructions.get(field_name, f"Extract the value for {field_name} and return in appropriate format")
        
        # Truncate content to avoid token limits
        max_content_length = 6000
        if len(content) > max_content_length:
            content = content[:max_content_length] + "... [content truncated]"
        
        prompt = f"""
Extract the field "{field_name}" from the following power plant content.

FIELD: {field_name}
SCHEMA REQUIREMENT: {instruction}
CONTEXT: {context}

CONTENT:
{content}

CRITICAL SCHEMA COMPLIANCE RULES:
1. You MUST return valid JSON in the exact format specified below
2. The "value" field must match the schema requirement exactly
3. Return null if the field cannot be found
4. Provide confidence score from 0.0 to 1.0
5. Never return malformed or partial JSON

REQUIRED JSON FORMAT:
{{
    "value": <extracted_value_matching_schema_or_null>,
    "confidence": <confidence_score_0_to_1>,
    "reasoning": "<brief_explanation_of_extraction>"
}}

Extract the field now and return ONLY the JSON response:
"""
        return prompt

    def _apply_schema_post_processing(self, field_name: str, value: Any) -> Any:
        """Apply schema-specific post-processing to ensure compliance."""
        
        if value is None:
            return None
        
        # Field-specific post-processing
        if field_name in ["capacity", "heat_rate", "unit_efficiency", "lat", "long", "plants_count"]:
            # Ensure numeric fields are numbers
            try:
                return float(value) if field_name in ["lat", "long", "unit_efficiency"] else int(float(value))
            except (ValueError, TypeError):
                return None
        
        elif field_name in ["plant_types", "units_id"]:
            # Ensure array fields are arrays
            if isinstance(value, str):
                # Try to parse as JSON array or split by comma
                try:
                    return json.loads(value) if value.startswith('[') else [v.strip() for v in value.split(',')]
                except:
                    return [value]
            elif isinstance(value, list):
                return value
            else:
                return [str(value)]
        
        elif field_name in ["commencement_date"]:
            # Ensure date format
            if isinstance(value, str) and len(value) >= 4:
                # Try to standardize date format
                try:
                    # Handle various date formats
                    if '-' in value:
                        return value  # Already in good format
                    elif len(value) == 4:  # Just year
                        return f"{value}-01-01"
                    else:
                        return value
                except:
                    return value
            return value
        
        else:
            # For string fields, ensure it's a string
            return str(value) if value is not None else None

    async def extract_multiple_fields_batch(self, field_list: List[str], content: str, context: str = "") -> Dict[str, GroqExtractionResult]:
        """
        Extract multiple fields with rate limiting and batch processing.
        
        Args:
            field_list: List of field names to extract
            content: Text content to extract from
            context: Additional context
            
        Returns:
            Dictionary mapping field names to GroqExtractionResults
        """
        results = {}
        
        # Process fields with rate limiting
        for i, field_name in enumerate(field_list):
            # Add delay between requests to avoid rate limits
            if i > 0:
                await asyncio.sleep(0.5)  # 500ms delay between requests
            
            result = await self.extract_field_with_schema_compliance(
                field_name=field_name,
                content=content,
                context=context
            )
            
            if result:
                results[field_name] = result
            
            # Log progress
            if (i + 1) % 5 == 0:
                logger.info(f"Processed {i + 1}/{len(field_list)} fields")
        
        return results

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get comprehensive usage statistics."""
        return {
            "total_extractions": self.extraction_count,
            "total_tokens_used": self.total_tokens_used,
            "rate_limit_delays": self.rate_limit_delays,
            "model": self.model,
            "schema_compliant": True,
            "timestamp": datetime.now().isoformat()
        }

    async def close(self):
        """Close the client connection."""
        # Groq client doesn't need explicit closing
        logger.info(f"Schema-compliant Groq client closed. Extractions: {self.extraction_count}, Rate limit delays: {self.rate_limit_delays}, Total tokens: {self.total_tokens_used}")
