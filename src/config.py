"""
Configuration management for the power plant data retrieval pipeline.
"""
import os
from typing import Dict, List
from dotenv import load_dotenv
from src.models import PipelineConfig

# Load environment variables
load_dotenv()


class Config:
    """Central configuration class."""

    def __init__(self):
        self.pipeline = PipelineConfig(
            max_search_results=int(os.getenv('MAX_SEARCH_RESULTS', 10)),
            max_scrape_pages=int(os.getenv('MAX_SCRAPE_PAGES', 5)),
            request_timeout=int(os.getenv('REQUEST_TIMEOUT', 60)),
            retry_attempts=int(os.getenv('RETRY_ATTEMPTS', 3)),
            min_content_length=int(os.getenv('MIN_CONTENT_LENGTH', 100)),
            max_content_length=int(os.getenv('MAX_CONTENT_LENGTH', 50000)),
            confidence_threshold=float(os.getenv('CONFIDENCE_THRESHOLD', 0.7)),
            # Use SCRAPER_API_KEY for both search and scraping since you're using ScraperAPI for both
            serp_api_key=os.getenv('SCRAPER_API_KEY', ''),
            scraper_api_key=os.getenv('SCRAPER_API_KEY', ''),
            groq_api_key=os.getenv('GROQ_API_KEY', ''),
            # OpenAI settings
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            openai_model=os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        )

    @property
    def search_query_templates(self) -> Dict[str, List[str]]:
        """Search query templates for different extraction phases."""
        return {
            "basic_discovery": [
                "{plant_name} power plant",
                "{plant_name} power station",
                "{plant_name} generating facility",
                "{plant_name} energy plant"
            ],
            "organizational": [
                "{plant_name} owner operator company",
                "{plant_name} power company",
                "{plant_name} utility company",
                "{plant_name} private public ownership",
                "{plant_name} government state owned",
                "{organization_name} power plants"  # Used when org found
            ],
            "technical_details": [
                "{plant_name} capacity MW specifications",
                "{plant_name} power plant type technology",
                "{plant_name} coal gas nuclear solar wind",
                "{plant_name} generation capacity"
            ],
            "location_details": [
                "{plant_name} location country province",
                "{plant_name} address location",
                "{plant_name} {country} {province}"  # Used when partially known
            ],
            "ppa_details": [
                "{plant_name} PPA power purchase agreement",
                "{plant_name} offtake agreement",
                "{plant_name} long term contract",
                "{plant_name} electricity sales agreement"
            ],
            "portfolio_details": [
                "{organization_name} power plants portfolio",
                "{organization_name} generating assets",
                "{organization_name} energy facilities",
                "{organization_name} power generation capacity"
            ],
            # New search categories for plant technical details
            "grid_connectivity": [
                "{plant_name} grid connection substation",
                "{plant_name} transmission line connection",
                "{plant_name} electrical grid tie-in",
                "{plant_name} substation interconnection",
                "{plant_name} grid infrastructure",
                "{plant_name} transmission system connection"
            ],
            "plant_coordinates": [
                "{plant_name} latitude longitude coordinates",
                "{plant_name} GPS location coordinates",
                "{plant_name} exact location address",
                "{plant_name} geographic coordinates",
                "{plant_name} site location map",
                "{plant_name} plant address location",
                "{plant_name} facility address",
                "{plant_name} power plant location details"
            ],
            "ppa_contracts": [
                "{plant_name} power purchase agreement details",
                "{plant_name} PPA contract terms",
                "{plant_name} offtake agreement pricing",
                "{plant_name} electricity sales contract",
                "{plant_name} long term power contract",
                "{plant_name} utility purchase agreement"
            ],
            "plant_units": [
                "{plant_name} generation units turbines",
                "{plant_name} individual units capacity",
                "{plant_name} unit specifications",
                "{plant_name} turbine generator details",
                "{plant_name} plant units configuration",
                "{plant_name} generation equipment"
            ],
            "plant_specifications": [
                "{plant_name} technical specifications",
                "{plant_name} capacity MW rating",
                "{plant_name} plant design details",
                "{plant_name} engineering specifications",
                "{plant_name} facility technical data"
            ]
        }

    @property
    def source_type_indicators(self) -> Dict[str, List[str]]:
        """URL patterns to identify source types."""
        return {
            "company_official": [
                "investor", "about", "corporate", "company",
                "annual-report", "sustainability", "operations",
                "getmedia", "media", "documents", "reports"
            ],
            "regulatory_filing": [
                "sec.gov", "edgar", "10-k", "10-q", "8-k",
                "energy-commission", "utility-commission",
                "filing", "regulatory"
            ],
            "government_database": [
                ".gov", "eia.gov", "energy.gov", "epa.gov",
                "iea.org", "irena.org"
            ],
            "industry_report": [
                "platts", "woodmac", "globaldata", "bnef",
                "power-eng", "utility-dive", "energy-central"
            ],
            "news_article": [
                "reuters", "bloomberg", "wsj", "ft.com",
                "power-technology", "renewableenergyworld"
            ],
            "wikipedia": [
                "wikipedia.org", "wikimedia"
            ]
        }

    @property
    def url_priority_weights(self) -> Dict[str, int]:
        """Priority weights for different source types."""
        return {
            "company_official": 10,
            "regulatory_filing": 9,
            "government_database": 8,
            "industry_report": 7,
            "news_article": 6,
            "wikipedia": 5,
            "other": 3
        }

    @property
    def content_relevance_keywords(self) -> Dict[str, List[str]]:
        """Keywords for content relevance scoring."""
        return {
            "high_relevance": [
                "power plant", "generating station", "power station",
                "MW", "megawatt", "capacity", "generation",
                "electricity", "energy", "utility", "grid",
                "thermal power", "power generation", "electric power"
            ],
            "medium_relevance": [
                "coal", "gas", "nuclear", "solar", "wind", "hydro",
                "biomass", "geothermal", "renewable", "fossil",
                "steam", "turbine", "generator", "boiler"
            ],
            "organizational": [
                "owner", "operator", "subsidiary", "parent company",
                "acquired", "merger", "joint venture", "partnership",
                "board of directors", "management", "executive"
            ],
            "financial": [
                "annual report", "financial year", "fiscal year",
                "revenue", "earnings", "investment", "funding",
                "financial statements", "balance sheet", "income statement"
            ],
            "location": [
                "located", "situated", "based", "province", "state",
                "region", "country", "city", "municipality",
                "address", "coordinates", "district"
            ]
        }

    @property
    def extraction_prompts(self) -> Dict[str, str]:
        """LLM prompts for field extraction."""
        return {
            "cfpp_type": """
From the following content about {plant_name}, determine the ownership type of this power plant.
Look for information about whether this is a private company, public/government entity, or other ownership structure.
Look for phrases like: "private company", "publicly owned", "government owned", "state-owned", "municipal", "cooperative", "joint venture", "public-private partnership".

Content: {content}

Return the ownership type:
- "private" for privately owned companies
- "public" for government/state/municipal owned entities
- "cooperative" for cooperative ownership
- "joint_venture" for joint ventures or partnerships
- "unknown" if ownership type is unclear or not found

Return only one of these values: "private", "public", "cooperative", "joint_venture", "unknown".
""",

            "organization_name": """
From the following content about {plant_name}, identify the official company name that owns or operates this power plant.
Look for phrases like: "owned by" or "operated by", company names in titles, or "power plant" in titles.
Do not search for subsidiaries, shareholders, or other related entities. Return only the name of the parent company.

Content: {content}

IMPORTANT: Return ONLY the company name, nothing else. No explanations, no additional text.


If unclear or not found, return "unknown".
""",

            "country_name": """
From the following content about {plant_name}, identify the country where this power plant is located.
Look for explicit country mentions, addresses, or geographic references.

Content: {content}

IMPORTANT: Return ONLY the country name, nothing else. No explanations, no additional text.


If unclear or not found, return "unknown".
""",

            "province": """
From the following content about {plant_name}, identify the state, province, or sub-national region where this power plant is located.
Look for state names, province names, regional identifiers in addresses or location descriptions.

Content: {content}

IMPORTANT: Return ONLY the state/province/region name, nothing else. No explanations, no additional text.

If unclear or not found, return "unknown".
""",

            "plants_count": """
From the following content about {organization_name}, determine the number of power plants owned by this organization.
Look for phrases like: "operates X plants", "portfolio of X facilities", "X generating stations", "X power plants".
Count only power plants, not individual generating units.

Content: {content}

Return only a number (integer).
If unclear or not found, return "unknown".
""",

            "plant_types": """
From the following content about {organization_name}, identify all types of power generation technologies operated by this organization.
Look for mentions of: coal, gas, nuclear, solar, wind, hydro, biomass, geothermal, oil, etc.
Return as a list of technologies.

Content: {content}

Return as a comma-separated list .
If unclear or not found, return "unknown".
""",

            "ppa_flag": """
From the following content about {plant_name}, determine the level at which the Power Purchase Agreement (PPA) applies.
Look for: "PPA", "power purchase agreement", "offtake agreement", "long-term contract", "electricity sales agreement".

Determine if the PPA applies at:
- "Plant" level (site-wide agreement covering the entire power plant facility)
- "Unit" level (individual agreements for specific generating units within the plant)

Content: {content}

Return "Plant" if PPA applies to the entire plant/site, "Unit" if PPA applies to individual generating units, "unknown" if unclear or no PPA information found.
""",

            "currency_in": """
From the following content about {plant_name}, identify the primary currency used for financial reporting.
Look for currency symbols, currency codes (USD, EUR, INR, etc.), financial statements, or explicit mentions of reporting currency.

Content: {content}

IMPORTANT: Return ONLY the 3-letter ISO currency code, nothing else.
For Indian companies, typically return "INR".
For US companies, typically return "USD".

If unclear or not found, return "INR".
""",

            "financial_year": """
From the following content about {plant_name}, identify the fiscal year period used by the organization.
Look for phrases like: "fiscal year ending March", "financial year April to March", "calendar year", "year ended December".

Content: {content}

IMPORTANT: Return ONLY the MM-MM format, nothing else.
Based on the country, determine the fiscal year period:
- India: typically "04-03" (April to March)
- USA: typically "01-12" (January to December)
- UK: typically "04-03" (April to March)

If unclear or not found, return "04-03".
"""
        }

    @property
    def plant_details_extraction_prompts(self) -> Dict[str, str]:
        """LLM prompts for plant technical details extraction."""
        return {
            "name": """
From the following content about {plant_name}, extract the official name of the power plant.
Look for the complete, official name as it appears in documents, not abbreviations.

Content: {content}

IMPORTANT: Return ONLY the plant name, nothing else. No explanations, no additional text.
Examples of correct responses:
- "Jhajjar Power Station"
- "Mahatma Gandhi Super Thermal Power Project"
- "Adani Mundra Power Station"

If not found, return: {plant_name}
""",
            "plant_type": """
From the following content about {plant_name}, determine the technology or fuel type of this power plant.
Look for information about the primary generation technology.

Content: {content}

IMPORTANT: Return ONLY the plant type, nothing else. No explanations, no additional text.
Valid responses: coal, gas, nuclear, solar, wind, hydro, biomass, geothermal, oil, combined_cycle, cogeneration



If not found, return "".
""",
            "plant_address": """
From the following content about {plant_name}, extract the district or city, state, and country location of the power plant.
Look for location information in the format: District or city, State, Country.

Content: {content}

IMPORTANT: Return ONLY the address, nothing else. No explanations, no additional text.


If not found, return "".
""",
            "lat": """
From the following content about {plant_name}, extract the plant's own latitude coordinate in decimal degrees.
Look for GPS coordinates, geographic location data, or map coordinates specifically for the power plant facility.

Content: {content}

IMPORTANT: Return ONLY the latitude number in decimal degrees, nothing else. No explanations, no additional text.


Valid range: -90 to +90. If not found, return "".
""",
            "long": """
From the following content about {plant_name}, extract the plant's own longitude coordinate in decimal degrees.
Look for GPS coordinates, geographic location data, or map coordinates specifically for the power plant facility.

Content: {content}

IMPORTANT: Return ONLY the longitude number in decimal degrees, nothing else. No explanations, no additional text.


Valid range: -180 to +180. If not found, return "".
""",

            "units_id": """
From the following content about {plant_name}, determine the total number of operational generating units at this plant.
Look for phrases like: "2 units", "two units", "660 MW each", "Unit 1 and Unit 2", "dual unit", "twin unit".
Count only operational generating units, not auxiliary equipment.

For Jhajjar Power Plant: It has 2 operational units of 660 MW each.

Content: {content}

IMPORTANT: Return ONLY a JSON array of integers from 1 to the number of units, nothing else.
Examples of correct responses:
- [1, 2] (for a 2-unit plant like Jhajjar)
- [1, 2, 3, 4] (for a 4-unit plant)
- [1] (for a single-unit plant)

If not found or unclear, return: [1, 2]
""",
            "grid_connectivity_maps": """
From the following content about {plant_name}, extract detailed grid connection and substation information.
Look for substation names, transmission line voltages (kV), grid tie-in points, electrical infrastructure details.

Content: {content}

IMPORTANT: Return ONLY valid JSON, nothing else. No explanations, no additional text.

Return a JSON array with this exact structure:
[{
  "details": [{
    "substation_name": "The official name of the substation",
    "substation_type": "The classification and voltage level of the substation, including any regional or directional qualifier",
    "capacity": "The rated capacity of the connection at this substation (e.g., in MW)",
    "latitude": "The geographic latitude of the substation",
    "longitude": "The geographic longitude of the substation",
    "projects": [{
      "distance": "The distance (e.g., in km) from the substation to that project"
    }]
  }]
}]

If no grid information found, return: []
""",

            "ppa_details": """
From the following content about {plant_name}, extract Power Purchase Agreement (PPA) contract details.
Look for contract capacity, duration, start/end dates, counterparty information, pricing details.

Content: {content}

IMPORTANT: Return ONLY valid JSON, nothing else. No explanations, no additional text.

Return a JSON array with this exact structure:
[{
  "capacity": "The capacity covered by this PPA (typically in MW)",
  "capacity_unit": "The unit of that capacity (e.g., 'MW', 'kW')",
  "start_date": "The PPA's commencement date (ISO format, YYYY-MM-DD)",
  "end_date": "The PPA's termination date (ISO format, YYYY-MM-DD)",
  "tenure": "The numeric duration of the PPA (e.g., 20)",
  "tenure_type": "The unit for the tenure (e.g., 'Years', 'Months')",
  "respondents": [{
    "name": "The entity procuring the power from the plant/unit under the terms of the PPA",
    "capacity": "The capacity volume contracted by this respondent",
    "currency": "The currency in which the price is denominated (e.g., 'USD', 'INR')",
    "price": "The contracted price per unit of energy or capacity",
    "price_unit": "The basis for the price (e.g., '$/MWh', 'INR/kW-year')"
  }]
}]

If no PPA information found, return: []
"""
        }

    @property
    def unit_details_extraction_prompts(self) -> Dict[str, str]:
        """LLM prompts for unit-level details extraction based on unit_level.json schema."""
        return {
            # Basic unit information
            "capacity": """
From the following content about {plant_name} Unit {unit_id}, extract the unit-specific installed capacity in megawatts (MW).
Look for unit-specific capacity information, not total plant capacity.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric capacity value for this specific unit, nothing else.
For Jhajjar Power Plant, each unit is 660 MW.

If not found, return "".
""",

            "capacity_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the unit for installed capacity.
Look for capacity units like MW, kW, GW.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the capacity unit, typically 'MW'.

If not found, return "MW".
""",

            "technology": """
From the following content about {plant_name} Unit {unit_id}, extract the specific technology type.
Look for: Ultra Super Critical, Super Critical, Critical, Sub-critical for coal plants.
For Natural Gas: Single/Open Cycle, Combined/Closed Cycle.
For Biomass: Fluidized Bed Reactor, Direct Combustion, Boiler Conversion.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the technology type, nothing else.
Valid responses: Ultra Super Critical, Super Critical, Critical, Sub-critical, Single Cycle, Combined Cycle, etc.

If not found, return "".
""",

            "commencement_date": """
From the following content about {plant_name} Unit {unit_id}, extract the commercial operation date.
Look for commissioning date, COD, commercial operation date, or operational start date.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the date in format yyyy-mm-ddThh:mm:ss.msZ (e.g., 2012-07-19T00:00:00.000Z).

If not found, return "".
""",

            "fuel_type": """
From the following content about {plant_name} Unit {unit_id}, extract fuel type information.
Look for primary fuel used by this unit.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY valid JSON array, nothing else.
Format: [{"fuel": "Coal", "type": "bituminous", "years_percentage": {"2023": "100"}}]

If not found, return: []
""",

            "unit_efficiency": """
From the following content about {plant_name} Unit {unit_id}, extract the unit-specific efficiency percentage.
Look for net efficiency, thermal efficiency, or overall efficiency of this unit.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric efficiency percentage, nothing else.

If not found, return "".
""",

            "heat_rate": """
From the following content about {plant_name} Unit {unit_id}, extract the station heat rate.
Look for heat rate in kCal/kWh, BTU/kWh, or kJ/kWh for this specific unit.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric heat rate value, nothing else.

If not found, return "".
""",

            "heat_rate_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the heat rate unit.
Look for units like kCal/kWh, BTU/kWh, kJ/kWh.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the heat rate unit, nothing else.

If not found, return "kCal/kWh".
""",

            "plf": """
From the following content about {plant_name} Unit {unit_id}, extract Plant Load Factor (PLF) data.
Look for yearly PLF percentages for this unit.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY valid JSON array, nothing else.
Format: [{"value": "75.2", "year": "2023"}]

If not found, return: []
""",

            "auxiliary_power_consumed": """
From the following content about {plant_name} Unit {unit_id}, extract auxiliary power consumption data.
Look for auxiliary energy consumption as percentage of gross generation.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY valid JSON array, nothing else.
Format: [{"value": "6.5", "year": "2023"}]

If not found, return: []
""",

            "emission_factor": """
From the following content about {plant_name} Unit {unit_id}, extract CO2 emission factor data.
Look for CO2 emissions in kg CO2e/kWh for this unit.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY valid JSON array, nothing else.
Format: [{"value": "0.82", "year": "2023"}]

If not found, return: []
""",

            "boiler_type": """
From the following content about {plant_name} Unit {unit_id}, extract the boiler type.
Look for boiler technology, manufacturer, or type specifications.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the boiler type, nothing else.

If not found, return "".
""",

            "selected_coal_type": """
From the following content about {plant_name} Unit {unit_id}, extract the coal type used.
Look for coal grade: bituminous, sub-bituminous, lignite, anthracite.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the coal type, nothing else.

If not found, return "".
""",

            "gcv_coal": """
From the following content about {plant_name} Unit {unit_id}, extract the Gross Calorific Value of coal.
Look for GCV in kCal/kg for the coal used in this unit.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric GCV value, nothing else.

If not found, return "".
""",

            "gcv_coal_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the GCV unit.
Look for units like kCal/kg, BTU/lb, MJ/kg.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the GCV unit, nothing else.

If not found, return "kCal/kg".
""",

            "unit_lifetime": """
From the following content about {plant_name} Unit {unit_id}, extract the operational lifetime.
Look for design life, operational life, or expected lifetime in years.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric lifetime in years, nothing else.

If not found, return "".
""",

            "unit_number": """
From the following content about {plant_name} Unit {unit_id}, extract the unit number labeling.
Look for unit designation, unit name, or unit identifier.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the unit number/label, nothing else.

If not found, return "Unit {unit_id}".
""",

            "plant_id": """
From the following content about {plant_name} Unit {unit_id}, extract the plant identifier.
Look for plant ID, plant code, or unique plant identifier.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the plant identifier, nothing else.

If not found, return "1".
""",

            "remaining_useful_life": """
From the following content about {plant_name} Unit {unit_id}, extract the end-of-life date.
Look for retirement date, decommissioning date, or end of useful life.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the date in format yyyy-mm-ddThh:mm:ss.msZ.

If not found, return "".
""",

            "gross_power_generation": """
From the following content about {plant_name} Unit {unit_id}, extract gross power generation data.
Look for total energy generated by the unit in a financial year.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY valid JSON array, nothing else.
Format: [{"value": "4500", "year": "2023"}]

If not found, return: []
""",

            "PAF": """
From the following content about {plant_name} Unit {unit_id}, extract Plant Availability Factor data.
Look for PAF percentages indicating how often the plant is available to generate electricity.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY valid JSON array, nothing else.
Format: [{"value": "85.5", "year": "2023"}]

If not found, return: []
""",

            # Additional fields from unit_level.json schema
            "selected_biomass_type": """
From the following content about {plant_name} Unit {unit_id}, extract the selected biomass type used for cofiring.
Look for biomass fuel types like wood pellets, palm kernel shells, crop husks, etc.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the biomass type, nothing else.

If not found, return "".
""",

            "gcv_biomass": """
From the following content about {plant_name} Unit {unit_id}, extract the gross calorific value of biomass.
Look for GCV of biomass in kCal/kg.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric GCV value, nothing else.

If not found, return "".
""",

            "gcv_biomass_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the GCV biomass unit.
Look for units like kCal/kg for biomass calorific value.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the unit, nothing else.

If not found, return "kCal/kg".
""",

            "gcv_natural_gas": """
From the following content about {plant_name} Unit {unit_id}, extract the gross calorific value of natural gas.
Look for GCV in MJ/m³, MJ/kg, kCal/m³, or Btu/ft³.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric GCV value, nothing else.

If not found, return "".
""",

            "gcv_natural_gas_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the GCV natural gas unit.
Look for units like MJ/m³, MJ/kg, kCal/m³, or Btu/ft³.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the unit, nothing else.

If not found, return "MJ/m³".
""",

            "efficiency_loss_cofiring": """
From the following content about {plant_name} Unit {unit_id}, extract the efficiency loss from cofiring.
Look for reduction in efficiency percentage due to biomass cofiring.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric efficiency loss percentage, nothing else.

If not found, return "".
""",

            "capex_required_retrofit": """
From the following content about {plant_name} Unit {unit_id}, extract CAPEX required to retrofit for biomass cofiring.
Look for capital expenditure requirements for retrofitting.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric CAPEX value, nothing else.

If not found, return "".
""",

            "capex_required_retrofit_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the CAPEX retrofit unit.
Look for units like Million USD, Million INR, etc.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the unit, nothing else.

If not found, return "Million".
""",

            "capex_required_renovation_open_cycle": """
From the following content about {plant_name} Unit {unit_id}, extract CAPEX for open cycle renovation.
Look for capital expenditure to convert to open/simple cycle natural gas plant.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric CAPEX value, nothing else.

If not found, return "".
""",

            "capex_required_renovation_open_cycle_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the open cycle renovation CAPEX unit.
Look for units like USD/MW, INR/MW, etc.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the unit, nothing else.

If not found, return "USD/MW".
""",

            "capex_required_renovation_closed_cycle": """
From the following content about {plant_name} Unit {unit_id}, extract CAPEX for closed cycle renovation.
Look for capital expenditure to convert to closed/combined cycle natural gas plant.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric CAPEX value, nothing else.

If not found, return "".
""",

            "capex_required_renovation_closed_cycle_unit": """
From the following content about {plant_name} Unit {unit_id}, extract the closed cycle renovation CAPEX unit.
Look for units like USD/MW, INR/MW, etc.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the unit, nothing else.

If not found, return "USD/MW".
""",

            "open_cycle_gas_turbine_efficency": """
From the following content about {plant_name} Unit {unit_id}, extract open cycle gas turbine efficiency.
Look for OCGT efficiency percentage for the specific country.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric efficiency percentage, nothing else.

If not found, return "".
""",

            "closed_cylce_gas_turbine_efficency": """
From the following content about {plant_name} Unit {unit_id}, extract closed cycle gas turbine efficiency.
Look for CCGT efficiency percentage for the specific country.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric efficiency percentage, nothing else.

If not found, return "".
""",

            "open_cycle_heat_rate": """
From the following content about {plant_name} Unit {unit_id}, extract open cycle heat rate.
Look for OCGT heat rate indicating fuel energy input per kWh output.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric heat rate value, nothing else.

If not found, return "".
""",

            "combined_cycle_heat_rate": """
From the following content about {plant_name} Unit {unit_id}, extract combined cycle heat rate.
Look for CCGT heat rate for the specific country.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the numeric heat rate value, nothing else.

If not found, return "".
""",

            "unit": """
From the following content about {plant_name} Unit {unit_id}, extract the unit efficiency measurement unit.
Look for efficiency measurement units, typically percentage (%).

Context: {context}
Content: {content}

IMPORTANT: Return ONLY the unit, typically '%'.

If not found, return "%".
""",

            "ppa_details": """
From the following content about {plant_name} Unit {unit_id}, extract unit-specific PPA details.
Look for Power Purchase Agreement information specific to this unit.

Context: {context}
Content: {content}

IMPORTANT: Return ONLY valid JSON array, nothing else.
Format: [{"capacity": "660", "capacity_unit": "MW", "end_date": "2042-01-01T00:00:00.000Z", "respondents": [{"capacity": "660", "currency": "INR", "name": "UHBVNL", "price": "3.5", "price_unit": "INR/kWh"}], "start_date": "2012-01-01T00:00:00.000Z", "tenure": 30, "tenure_type": "Fixed"}]

If not found, return: []
"""
        }

    @property
    def missing_field_extraction_prompts(self) -> Dict[str, str]:
        """Enhanced prompts for missing nested field extraction with context."""
        return {
            "coordinate_latitude": """
From the following content, extract the latitude coordinate for {field_context}.
Look for GPS coordinates, geographic location data, or map coordinates.

Existing context: {existing_context}
Field: {field_name}
Content: {content}

IMPORTANT: Return ONLY the latitude number in decimal degrees, nothing else.
Valid range: -90 to +90.

If not found, return "".
""",

            "coordinate_longitude": """
From the following content, extract the longitude coordinate for {field_context}.
Look for GPS coordinates, geographic location data, or map coordinates.

Existing context: {existing_context}
Field: {field_name}
Content: {content}

IMPORTANT: Return ONLY the longitude number in decimal degrees, nothing else.
Valid range: -180 to +180.

If not found, return "".
""",

            "financial_price": """
From the following content, extract the price/tariff for {field_context}.
Look for tariff rates, prices per unit, or financial terms.

Existing context: {existing_context}
Field: {field_name}
Content: {content}

IMPORTANT: Return ONLY the numeric price value, nothing else.
Look for prices in INR/kWh, $/MWh, or similar units.

If not found, return "".
""",

            "identifier_name": """
From the following content, extract the actual name/identifier for {field_context}.
Look for specific company names, utility names, or official identifiers.

Existing context: {existing_context}
Field: {field_name}
Content: {content}

IMPORTANT: Return ONLY the actual name, nothing else.
For utilities, look for: UHBVNL, DHBVNL, Tata Power Company, etc.

If not found, return "".
""",

            "date_time": """
From the following content, extract the date for {field_context}.
Look for specific dates, timelines, or schedules.

Existing context: {existing_context}
Field: {field_name}
Content: {content}

IMPORTANT: Return ONLY the date in format yyyy-mm-ddThh:mm:ss.msZ, nothing else.

If not found, return "".
""",

            "generic_field": """
From the following content, extract the value for {field_context}.
Look for information related to this specific field.

Existing context: {existing_context}
Field: {field_name}
Content: {content}

IMPORTANT: Return ONLY the field value, nothing else.

If not found, return "".
"""
        }


# Global configuration instance
config = Config()
