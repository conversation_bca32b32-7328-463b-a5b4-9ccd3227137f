#!/usr/bin/env python3
"""
Complete Pipeline Demo - Power Plant Data Extraction
Demonstrates the full pipeline workflow with mock data when API keys are not available.
"""
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockPipelineDemo:
    """Mock pipeline demo to show complete workflow."""
    
    def __init__(self):
        """Initialize the demo."""
        self.plant_name = "Jhajjar Power Plant"
        
    async def run_complete_demo(self):
        """Run the complete pipeline demo."""
        print("🚀 COMPLETE POWER PLANT DATA EXTRACTION PIPELINE DEMO")
        print("=" * 70)
        print(f"🔍 Target Plant: {self.plant_name}")
        print(f"🧠 Demonstrating: Full extraction workflow with all field types")
        print(f"📊 Features: Organizational + Technical details + Missing field detection")
        print("=" * 70)
        
        start_time = time.time()
        
        # Step 1: Mock organizational details extraction
        print("\n📊 STEP 1: ORGANIZATIONAL DETAILS EXTRACTION")
        print("-" * 50)
        org_details = await self._extract_organizational_details()
        self._display_organizational_results(org_details)
        
        # Step 2: Mock plant technical details extraction
        print("\n🔧 STEP 2: PLANT TECHNICAL DETAILS EXTRACTION")
        print("-" * 50)
        plant_details = await self._extract_plant_technical_details()
        self._display_plant_results(plant_details)
        
        # Step 3: Mock missing field detection and filling
        print("\n🎯 STEP 3: MISSING FIELD DETECTION & TARGETED SEARCH")
        print("-" * 50)
        processed_plant_details = await self._process_missing_fields(plant_details)
        self._display_missing_field_results(plant_details, processed_plant_details)
        
        # Step 4: Save results
        print("\n💾 STEP 4: SAVING RESULTS")
        print("-" * 50)
        await self._save_results(org_details, processed_plant_details)
        
        total_time = time.time() - start_time
        print(f"\n🎉 COMPLETE PIPELINE DEMO FINISHED!")
        print(f"⏱️  Total time: {total_time:.1f} seconds")
        print(f"📊 Total fields extracted: {self._count_filled_fields(org_details, processed_plant_details)}")
        
        return org_details, processed_plant_details
    
    async def _extract_organizational_details(self) -> Dict[str, Any]:
        """Mock organizational details extraction."""
        print("🔍 Simulating web search and content extraction...")
        await asyncio.sleep(1)  # Simulate API calls
        
        print("🧠 Simulating LLM processing for organizational fields...")
        await asyncio.sleep(1)
        
        org_details = {
            "cfpp_type": "joint_venture",
            "country_name": "India",
            "currency_in": "INR",
            "financial_year": "04-03",
            "organization_name": "CLP India Private Limited",
            "plants_count": 3,
            "plant_types": ["coal", "solar"],
            "ppa_flag": "Plant",
            "province": "Haryana"
        }
        
        print("✅ Organizational details extraction completed")
        return org_details
    
    async def _extract_plant_technical_details(self) -> Dict[str, Any]:
        """Mock plant technical details extraction."""
        print("🔍 Simulating targeted searches for technical details...")
        await asyncio.sleep(1)
        
        print("🧠 Simulating LLM processing for technical fields...")
        await asyncio.sleep(1)
        
        plant_details = {
            "name": "Jhajjar Power Plant",
            "plant_type": "coal",
            "plant_address": "Jharli village, Jhajjar district, Haryana, India",
            "lat": "28.607111",
            "long": "76.656914",
            "plant_id": 1,
            "units_id": [1, 2],
            "grid_connectivity_maps": [
                {
                    "description": "Grid connectivity details for Jhajjar Power Plant",
                    "details": [
                        {
                            "substation_name": "Sonipat Substation",
                            "substation_type": "transmission",
                            "capacity": "400 kV",
                            "latitude": "",  # Missing field
                            "longitude": "",  # Missing field
                            "description": "Primary transmission connection point",
                            "projects": [
                                {
                                    "description": "400 kV transmission line from Jhajjar to Sonipat",
                                    "distance": "70 km"
                                }
                            ]
                        },
                        {
                            "substation_name": "Mahendragarh Substation", 
                            "substation_type": "transmission",
                            "capacity": "400 kV",
                            "latitude": "",  # Missing field
                            "longitude": "",  # Missing field
                            "description": "Secondary transmission connection point",
                            "projects": [
                                {
                                    "description": "400 kV transmission line from Jhajjar to Mahendragarh",
                                    "distance": "50 km"
                                }
                            ]
                        }
                    ]
                }
            ],
            "ppa_details": [
                {
                    "description": "Power Purchase Agreement for Jhajjar Power Plant",
                    "capacity": "1320 MW",
                    "capacity_unit": "MW",
                    "start_date": "2012",
                    "end_date": "",
                    "tenure": 30,
                    "tenure_type": "Years",
                    "respondents": [
                        {
                            "name": "Haryana State Distribution Companies",
                            "capacity": "1188 MW",
                            "currency": "INR",
                            "price": "",  # Missing field
                            "price_unit": "INR/kWh"
                        },
                        {
                            "name": "External Power Buyers",
                            "capacity": "132 MW", 
                            "currency": "INR",
                            "price": "",  # Missing field
                            "price_unit": "INR/kWh"
                        }
                    ]
                }
            ]
        }
        
        print("✅ Plant technical details extraction completed")
        return plant_details
    
    async def _process_missing_fields(self, plant_details: Dict[str, Any]) -> Dict[str, Any]:
        """Mock missing field detection and filling."""
        print("🔍 Detecting missing fields in nested structures...")
        
        # Identify missing fields
        missing_fields = []
        
        # Check grid connectivity for missing coordinates
        for grid_map in plant_details.get("grid_connectivity_maps", []):
            for detail in grid_map.get("details", []):
                if not detail.get("latitude"):
                    missing_fields.append(f"Grid latitude for {detail.get('substation_name')}")
                if not detail.get("longitude"):
                    missing_fields.append(f"Grid longitude for {detail.get('substation_name')}")
        
        # Check PPA details for missing prices
        for ppa in plant_details.get("ppa_details", []):
            for respondent in ppa.get("respondents", []):
                if not respondent.get("price"):
                    missing_fields.append(f"PPA price for {respondent.get('name')}")
        
        print(f"📋 Found {len(missing_fields)} missing fields:")
        for field in missing_fields:
            print(f"   • {field}")
        
        print("\n🎯 Performing targeted searches for missing fields...")
        await asyncio.sleep(2)  # Simulate targeted searches
        
        # Fill missing fields with mock data
        processed_details = plant_details.copy()
        
        # Fill grid coordinates
        for grid_map in processed_details.get("grid_connectivity_maps", []):
            for detail in grid_map.get("details", []):
                if detail.get("substation_name") == "Sonipat Substation":
                    detail["latitude"] = "28.9931"
                    detail["longitude"] = "77.0151"
                elif detail.get("substation_name") == "Mahendragarh Substation":
                    detail["latitude"] = "28.2833"
                    detail["longitude"] = "76.1500"
        
        # Fill PPA prices
        for ppa in processed_details.get("ppa_details", []):
            for respondent in ppa.get("respondents", []):
                if respondent.get("name") == "Haryana State Distribution Companies":
                    respondent["price"] = "2.89"
                elif respondent.get("name") == "External Power Buyers":
                    respondent["price"] = "3.15"
        
        print("✅ Missing field processing completed")
        return processed_details
    
    def _display_organizational_results(self, org_details: Dict[str, Any]):
        """Display organizational results."""
        filled_fields = sum(1 for v in org_details.values() if v not in [None, "", []])
        print(f"📊 Extracted {filled_fields}/{len(org_details)} organizational fields:")
        
        field_labels = {
            'organization_name': '🏢 Organization',
            'country_name': '🌍 Country', 
            'province': '📍 Province',
            'plant_types': '⚡ Plant Types',
            'cfpp_type': '🏛️ Type',
            'plants_count': '🏭 Plants Count',
            'financial_year': '📅 Financial Year',
            'currency_in': '💰 Currency',
            'ppa_flag': '📄 PPA Flag'
        }
        
        for field, label in field_labels.items():
            value = org_details.get(field)
            if value and value not in [None, "", []]:
                if isinstance(value, list):
                    print(f"   {label}: {', '.join(map(str, value))}")
                else:
                    print(f"   {label}: {value}")
    
    def _display_plant_results(self, plant_details: Dict[str, Any]):
        """Display plant technical results."""
        filled_fields = sum(1 for v in plant_details.values() if v not in [None, "", []])
        print(f"🔧 Extracted {filled_fields}/{len(plant_details)} plant technical fields:")
        
        field_labels = {
            'name': '📛 Plant Name',
            'plant_type': '⚙️ Plant Type', 
            'plant_address': '📍 Address',
            'lat': '🌐 Latitude',
            'long': '🌐 Longitude',
            'units_id': '🔢 Units'
        }
        
        for field, label in field_labels.items():
            value = plant_details.get(field)
            if value and value not in [None, "", []]:
                if isinstance(value, list):
                    print(f"   {label}: {', '.join(map(str, value))}")
                else:
                    print(f"   {label}: {value}")
        
        # Show complex fields summary
        if plant_details.get('grid_connectivity_maps'):
            total_substations = sum(len(grid.get('details', [])) for grid in plant_details['grid_connectivity_maps'])
            print(f"   🔌 Grid Connectivity: {total_substations} substations")
        
        if plant_details.get('ppa_details'):
            total_respondents = sum(len(ppa.get('respondents', [])) for ppa in plant_details['ppa_details'])
            print(f"   📄 PPA Details: {total_respondents} respondents")
    
    def _display_missing_field_results(self, original: Dict[str, Any], processed: Dict[str, Any]):
        """Display missing field processing results."""
        print("🎯 Missing field processing results:")
        
        fields_filled = 0
        
        # Check grid coordinates
        for i, grid_map in enumerate(processed.get("grid_connectivity_maps", [])):
            for j, detail in enumerate(grid_map.get("details", [])):
                orig_detail = original["grid_connectivity_maps"][i]["details"][j]
                if not orig_detail.get("latitude") and detail.get("latitude"):
                    print(f"   ✅ Filled latitude for {detail.get('substation_name')}: {detail.get('latitude')}")
                    fields_filled += 1
                if not orig_detail.get("longitude") and detail.get("longitude"):
                    print(f"   ✅ Filled longitude for {detail.get('substation_name')}: {detail.get('longitude')}")
                    fields_filled += 1
        
        # Check PPA prices
        for i, ppa in enumerate(processed.get("ppa_details", [])):
            for j, respondent in enumerate(ppa.get("respondents", [])):
                orig_respondent = original["ppa_details"][i]["respondents"][j]
                if not orig_respondent.get("price") and respondent.get("price"):
                    print(f"   ✅ Filled price for {respondent.get('name')}: {respondent.get('price')} INR/kWh")
                    fields_filled += 1
        
        print(f"📊 Total missing fields filled: {fields_filled}")
    
    async def _save_results(self, org_details: Dict[str, Any], plant_details: Dict[str, Any]):
        """Save results to JSON files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save organizational details
        org_file = f"demo_org_details_{timestamp}.json"
        with open(org_file, 'w', encoding='utf-8') as f:
            json.dump(org_details, f, indent=2, ensure_ascii=False)
        print(f"📊 Organizational details saved to: {org_file}")
        
        # Save plant details
        plant_file = f"demo_plant_details_{timestamp}.json"
        with open(plant_file, 'w', encoding='utf-8') as f:
            json.dump(plant_details, f, indent=2, ensure_ascii=False)
        print(f"🔧 Plant details saved to: {plant_file}")
        
        # Save combined results
        combined_file = f"demo_complete_results_{timestamp}.json"
        combined_results = {
            "organizational_details": org_details,
            "plant_technical_details": plant_details,
            "extraction_metadata": {
                "plant_name": self.plant_name,
                "extraction_timestamp": datetime.now().isoformat(),
                "pipeline_type": "complete_demo",
                "total_fields": len(org_details) + len(plant_details)
            }
        }
        with open(combined_file, 'w', encoding='utf-8') as f:
            json.dump(combined_results, f, indent=2, ensure_ascii=False)
        print(f"📋 Combined results saved to: {combined_file}")
    
    def _count_filled_fields(self, org_details: Dict[str, Any], plant_details: Dict[str, Any]) -> int:
        """Count total filled fields."""
        org_filled = sum(1 for v in org_details.values() if v not in [None, "", []])
        plant_filled = sum(1 for v in plant_details.values() if v not in [None, "", []])
        return org_filled + plant_filled


async def main():
    """Main demo function."""
    print("🚀 POWER PLANT DATA EXTRACTION - COMPLETE PIPELINE DEMO")
    print("This demo shows the complete workflow without requiring API keys")
    print()
    
    try:
        demo = MockPipelineDemo()
        org_details, plant_details = await demo.run_complete_demo()
        
        print(f"\n📄 FINAL JSON RESULTS PREVIEW")
        print("=" * 50)
        print("\n📊 ORGANIZATIONAL DETAILS:")
        print(json.dumps(org_details, indent=2, ensure_ascii=False))
        
        print(f"\n🔧 PLANT TECHNICAL DETAILS:")
        print(json.dumps(plant_details, indent=2, ensure_ascii=False))
        
        print(f"\n✅ DEMO COMPLETED SUCCESSFULLY!")
        print("Check the generated JSON files for complete results.")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        logger.error(f"Demo failed: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
