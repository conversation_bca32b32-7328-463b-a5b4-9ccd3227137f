#!/usr/bin/env python3
"""
Missing Field Detection Demo
Demonstrates the missing field detection and targeted search functionality.
"""
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MissingFieldDemo:
    """Demo for missing field detection and filling."""
    
    def __init__(self):
        """Initialize the demo."""
        self.plant_name = "Jhajjar Power Plant"
        
    async def run_missing_field_demo(self):
        """Run the missing field detection demo."""
        print("🎯 MISSING FIELD DETECTION & TARGETED SEARCH DEMO")
        print("=" * 60)
        print(f"🔍 Target Plant: {self.plant_name}")
        print(f"🧠 Features: Description removal + Missing field detection + Targeted searches")
        print(f"📊 Method: Pattern matching + Field-specific Google searches")
        print("=" * 60)
        
        start_time = time.time()
        
        # Step 1: Load sample data with missing fields
        print("\n📋 STEP 1: LOADING SAMPLE DATA WITH MISSING FIELDS")
        print("-" * 50)
        sample_data = self._get_sample_data_with_missing_fields()
        self._display_sample_data(sample_data)
        
        # Step 2: Remove description fields
        print("\n🧹 STEP 2: REMOVING ALL DESCRIPTION FIELDS")
        print("-" * 50)
        cleaned_data = self._remove_description_fields(sample_data)
        removed_count = self._count_removed_descriptions(sample_data, cleaned_data)
        print(f"✅ Removed {removed_count} description fields")
        
        # Step 3: Detect missing fields
        print("\n🔍 STEP 3: DETECTING MISSING FIELDS IN NESTED STRUCTURES")
        print("-" * 50)
        missing_fields = self._detect_missing_fields(cleaned_data)
        self._display_missing_fields(missing_fields)
        
        # Step 4: Generate targeted search queries
        print("\n🎯 STEP 4: GENERATING TARGETED SEARCH QUERIES")
        print("-" * 50)
        search_queries = self._generate_search_queries(missing_fields)
        self._display_search_queries(search_queries)
        
        # Step 5: Simulate targeted searches and fill missing fields
        print("\n🌐 STEP 5: SIMULATING TARGETED SEARCHES")
        print("-" * 50)
        filled_data = await self._simulate_targeted_searches(cleaned_data, search_queries)
        
        # Step 6: Display results
        print("\n📊 STEP 6: RESULTS COMPARISON")
        print("-" * 50)
        self._display_results_comparison(sample_data, filled_data)
        
        # Step 7: Save results
        print("\n💾 STEP 7: SAVING RESULTS")
        print("-" * 50)
        await self._save_results(sample_data, cleaned_data, filled_data)
        
        total_time = time.time() - start_time
        print(f"\n🎉 MISSING FIELD DEMO COMPLETED!")
        print(f"⏱️  Total time: {total_time:.1f} seconds")
        
        return filled_data
    
    def _get_sample_data_with_missing_fields(self) -> Dict[str, Any]:
        """Get sample data with missing fields."""
        return {
            "name": "Jhajjar Power Plant",
            "plant_type": "coal",
            "lat": "28.607111",
            "long": "76.656914",
            "plant_address": "Jharli village in Jhajjar district of Haryana",
            "units_id": [1, 2],
            "ppa_details": [
                {
                    "description": "Power Purchase Agreement for Jhajjar Power Plant",
                    "capacity": "1320 MW",
                    "capacity_unit": "MW",
                    "start_date": "2012",
                    "end_date": "",
                    "tenure": 30,
                    "tenure_type": "Years",
                    "respondents": [
                        {
                            "name": "Haryana State Distribution Companies",
                            "capacity": "1188 MW",
                            "currency": "INR",
                            "price": "",  # Missing field
                            "price_unit": "INR/kWh"
                        },
                        {
                            "name": "External Power Buyers",
                            "capacity": "132 MW",
                            "currency": "INR",
                            "price": "",  # Missing field
                            "price_unit": "INR/kWh"
                        }
                    ]
                }
            ],
            "plant_id": 1,
            "grid_connectivity_maps": [
                {
                    "description": "Grid connectivity details for Jhajjar Power Plant",
                    "details": [
                        {
                            "substation_name": "Sonipat Substation",
                            "substation_type": "transmission",
                            "capacity": "400 kV",
                            "latitude": "",  # Missing field
                            "longitude": "",  # Missing field
                            "description": "Primary transmission connection point - approximately 70 km northeast",
                            "projects": [
                                {
                                    "description": "400 kV transmission line from Jhajjar to Sonipat",
                                    "distance": "70 km"
                                }
                            ]
                        },
                        {
                            "substation_name": "Mahendragarh Substation",
                            "substation_type": "transmission",
                            "capacity": "400 kV",
                            "latitude": "",  # Missing field
                            "longitude": "",  # Missing field
                            "description": "Secondary transmission connection point - approximately 50 km southwest",
                            "projects": [
                                {
                                    "description": "400 kV transmission line from Jhajjar to Mahendragarh",
                                    "distance": "50 km"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    
    def _display_sample_data(self, data: Dict[str, Any]):
        """Display sample data overview."""
        print("📋 Sample data loaded with intentional missing fields:")
        print(f"   🔧 Plant: {data.get('name')}")
        print(f"   ⚡ Type: {data.get('plant_type')}")
        print(f"   📍 Location: {data.get('plant_address')}")
        
        # Count missing fields
        missing_count = 0
        
        # Check grid connectivity
        for grid_map in data.get("grid_connectivity_maps", []):
            for detail in grid_map.get("details", []):
                if not detail.get("latitude"):
                    missing_count += 1
                if not detail.get("longitude"):
                    missing_count += 1
        
        # Check PPA details
        for ppa in data.get("ppa_details", []):
            for respondent in ppa.get("respondents", []):
                if not respondent.get("price"):
                    missing_count += 1
        
        print(f"   ❌ Missing fields detected: {missing_count}")
    
    def _remove_description_fields(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove all description fields from the data."""
        import copy
        cleaned_data = copy.deepcopy(data)
        
        # Remove description from grid_connectivity_maps
        if "grid_connectivity_maps" in cleaned_data:
            for grid_map in cleaned_data["grid_connectivity_maps"]:
                if "description" in grid_map:
                    del grid_map["description"]
                
                if "details" in grid_map:
                    for detail in grid_map["details"]:
                        if "description" in detail:
                            del detail["description"]
                        
                        if "projects" in detail:
                            for project in detail["projects"]:
                                if "description" in project:
                                    del project["description"]
        
        # Remove description from ppa_details
        if "ppa_details" in cleaned_data:
            for ppa in cleaned_data["ppa_details"]:
                if "description" in ppa:
                    del ppa["description"]
        
        return cleaned_data
    
    def _count_removed_descriptions(self, original: Dict[str, Any], cleaned: Dict[str, Any]) -> int:
        """Count how many description fields were removed."""
        def count_descriptions(obj):
            count = 0
            if isinstance(obj, dict):
                if "description" in obj:
                    count += 1
                for value in obj.values():
                    count += count_descriptions(value)
            elif isinstance(obj, list):
                for item in obj:
                    count += count_descriptions(item)
            return count
        
        original_count = count_descriptions(original)
        cleaned_count = count_descriptions(cleaned)
        return original_count - cleaned_count
    
    def _detect_missing_fields(self, data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Detect missing fields in nested structures."""
        missing_fields = {
            "grid_connectivity": [],
            "ppa_details": []
        }
        
        # Check grid_connectivity_maps for missing lat/long
        if "grid_connectivity_maps" in data:
            for grid_idx, grid_map in enumerate(data["grid_connectivity_maps"]):
                if "details" in grid_map:
                    for detail_idx, detail in enumerate(grid_map["details"]):
                        substation_name = detail.get("substation_name", "Unknown Substation")
                        
                        # Check for missing latitude
                        if not detail.get("latitude") or detail.get("latitude") == "":
                            missing_fields["grid_connectivity"].append({
                                "field": "latitude",
                                "substation_name": substation_name,
                                "grid_idx": grid_idx,
                                "detail_idx": detail_idx
                            })
                        
                        # Check for missing longitude
                        if not detail.get("longitude") or detail.get("longitude") == "":
                            missing_fields["grid_connectivity"].append({
                                "field": "longitude",
                                "substation_name": substation_name,
                                "grid_idx": grid_idx,
                                "detail_idx": detail_idx
                            })
        
        # Check ppa_details for missing prices
        if "ppa_details" in data:
            for ppa_idx, ppa in enumerate(data["ppa_details"]):
                if "respondents" in ppa:
                    for resp_idx, respondent in enumerate(ppa["respondents"]):
                        respondent_name = respondent.get("name", "Unknown Respondent")
                        
                        # Check for missing price
                        if not respondent.get("price") or respondent.get("price") == "":
                            missing_fields["ppa_details"].append({
                                "field": "price",
                                "respondent_name": respondent_name,
                                "ppa_idx": ppa_idx,
                                "resp_idx": resp_idx
                            })
        
        return missing_fields
    
    def _display_missing_fields(self, missing_fields: Dict[str, List[Dict[str, Any]]]):
        """Display detected missing fields."""
        total_missing = len(missing_fields["grid_connectivity"]) + len(missing_fields["ppa_details"])
        print(f"🔍 Detected {total_missing} missing fields:")
        
        print(f"\n   🔌 Grid Connectivity Missing Fields ({len(missing_fields['grid_connectivity'])}):")
        for missing in missing_fields["grid_connectivity"]:
            print(f"      • {missing['field']} for {missing['substation_name']}")
        
        print(f"\n   📄 PPA Details Missing Fields ({len(missing_fields['ppa_details'])}):")
        for missing in missing_fields["ppa_details"]:
            print(f"      • {missing['field']} for {missing['respondent_name']}")
    
    def _generate_search_queries(self, missing_fields: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """Generate targeted search queries for missing fields."""
        queries = []
        
        # Generate queries for grid connectivity missing fields
        for missing in missing_fields["grid_connectivity"]:
            substation_name = missing["substation_name"]
            field = missing["field"]
            
            if field == "latitude":
                query = f"{self.plant_name} {substation_name} latitude coordinates GPS location"
            elif field == "longitude":
                query = f"{self.plant_name} {substation_name} longitude coordinates GPS location"
            
            queries.append({
                "query": query,
                "field_type": f"grid_{field}",
                "context": missing
            })
        
        # Generate queries for PPA missing fields
        for missing in missing_fields["ppa_details"]:
            respondent_name = missing["respondent_name"]
            field = missing["field"]
            
            if field == "price":
                query = f"{self.plant_name} {respondent_name} PPA tariff price rate INR kWh"
            
            queries.append({
                "query": query,
                "field_type": f"ppa_{field}",
                "context": missing
            })
        
        return queries
    
    def _display_search_queries(self, queries: List[Dict[str, Any]]):
        """Display generated search queries."""
        print(f"🎯 Generated {len(queries)} targeted search queries:")
        for i, query_info in enumerate(queries, 1):
            print(f"   {i}. {query_info['field_type']}: {query_info['query']}")
    
    async def _simulate_targeted_searches(self, data: Dict[str, Any], queries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Simulate targeted searches and fill missing fields."""
        import copy
        filled_data = copy.deepcopy(data)
        
        print("🌐 Simulating targeted searches for missing fields...")
        
        for query_info in queries:
            field_type = query_info["field_type"]
            context = query_info["context"]
            
            print(f"   🔍 Searching for {field_type}...")
            await asyncio.sleep(0.5)  # Simulate search delay
            
            # Simulate field extraction based on type
            if field_type == "grid_latitude":
                if context["substation_name"] == "Sonipat Substation":
                    value = "28.9931"
                elif context["substation_name"] == "Mahendragarh Substation":
                    value = "28.2833"
                else:
                    value = "28.5000"
                
                # Update the data
                grid_idx = context["grid_idx"]
                detail_idx = context["detail_idx"]
                filled_data["grid_connectivity_maps"][grid_idx]["details"][detail_idx]["latitude"] = value
                print(f"      ✅ Found latitude: {value}")
                
            elif field_type == "grid_longitude":
                if context["substation_name"] == "Sonipat Substation":
                    value = "77.0151"
                elif context["substation_name"] == "Mahendragarh Substation":
                    value = "76.1500"
                else:
                    value = "76.5000"
                
                # Update the data
                grid_idx = context["grid_idx"]
                detail_idx = context["detail_idx"]
                filled_data["grid_connectivity_maps"][grid_idx]["details"][detail_idx]["longitude"] = value
                print(f"      ✅ Found longitude: {value}")
                
            elif field_type == "ppa_price":
                if "Haryana State" in context["respondent_name"]:
                    value = "2.89"
                elif "External" in context["respondent_name"]:
                    value = "3.15"
                else:
                    value = "3.00"
                
                # Update the data
                ppa_idx = context["ppa_idx"]
                resp_idx = context["resp_idx"]
                filled_data["ppa_details"][ppa_idx]["respondents"][resp_idx]["price"] = value
                print(f"      ✅ Found price: {value} INR/kWh")
        
        return filled_data
    
    def _display_results_comparison(self, original: Dict[str, Any], filled: Dict[str, Any]):
        """Display comparison between original and filled data."""
        print("📊 Results comparison:")
        
        fields_filled = 0
        
        # Compare grid connectivity
        print("\n   🔌 Grid Connectivity Fields:")
        for i, grid_map in enumerate(filled.get("grid_connectivity_maps", [])):
            for j, detail in enumerate(grid_map.get("details", [])):
                orig_detail = original["grid_connectivity_maps"][i]["details"][j]
                substation_name = detail.get("substation_name")
                
                if not orig_detail.get("latitude") and detail.get("latitude"):
                    print(f"      ✅ {substation_name} latitude: {detail.get('latitude')}")
                    fields_filled += 1
                
                if not orig_detail.get("longitude") and detail.get("longitude"):
                    print(f"      ✅ {substation_name} longitude: {detail.get('longitude')}")
                    fields_filled += 1
        
        # Compare PPA details
        print("\n   📄 PPA Details Fields:")
        for i, ppa in enumerate(filled.get("ppa_details", [])):
            for j, respondent in enumerate(ppa.get("respondents", [])):
                orig_respondent = original["ppa_details"][i]["respondents"][j]
                respondent_name = respondent.get("name")
                
                if not orig_respondent.get("price") and respondent.get("price"):
                    print(f"      ✅ {respondent_name}: {respondent.get('price')} INR/kWh")
                    fields_filled += 1
        
        print(f"\n📈 Summary: {fields_filled} missing fields successfully filled")
    
    async def _save_results(self, original: Dict[str, Any], cleaned: Dict[str, Any], filled: Dict[str, Any]):
        """Save all results to JSON files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save original data
        original_file = f"demo_original_data_{timestamp}.json"
        with open(original_file, 'w', encoding='utf-8') as f:
            json.dump(original, f, indent=2, ensure_ascii=False)
        print(f"📋 Original data saved to: {original_file}")
        
        # Save cleaned data (descriptions removed)
        cleaned_file = f"demo_cleaned_data_{timestamp}.json"
        with open(cleaned_file, 'w', encoding='utf-8') as f:
            json.dump(cleaned, f, indent=2, ensure_ascii=False)
        print(f"🧹 Cleaned data saved to: {cleaned_file}")
        
        # Save filled data (missing fields filled)
        filled_file = f"demo_filled_data_{timestamp}.json"
        with open(filled_file, 'w', encoding='utf-8') as f:
            json.dump(filled, f, indent=2, ensure_ascii=False)
        print(f"✅ Filled data saved to: {filled_file}")


async def main():
    """Main demo function."""
    print("🎯 MISSING FIELD DETECTION & TARGETED SEARCH DEMO")
    print("This demo shows how to detect and fill missing fields in nested JSON structures")
    print()
    
    try:
        demo = MissingFieldDemo()
        filled_data = await demo.run_missing_field_demo()
        
        print(f"\n📄 FINAL FILLED DATA PREVIEW")
        print("=" * 50)
        print(json.dumps(filled_data, indent=2, ensure_ascii=False))
        
        print(f"\n✅ MISSING FIELD DEMO COMPLETED SUCCESSFULLY!")
        print("Check the generated JSON files for complete results.")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        logger.error(f"Demo failed: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
