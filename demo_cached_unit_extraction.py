#!/usr/bin/env python3
"""
Cached Unit Extraction Demo
Demonstrates the complete workflow:
1. Load plant details from cache
2. Extract unit details using cached plant data
3. Save unit details to cache and files
"""
import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CachedUnitExtractionDemo:
    """Demo for unit extraction using cached plant data."""
    
    def __init__(self):
        """Initialize the demo."""
        self.plant_name = "Jhajjar Power Plant"
        
    async def run_cached_unit_extraction_demo(self):
        """Run the complete cached unit extraction demo."""
        print("🔧 CACHED UNIT EXTRACTION DEMO")
        print("=" * 60)
        print(f"🔍 Target Plant: {self.plant_name}")
        print(f"💾 Method: Using cached plant data for unit extraction")
        print(f"📊 Features: Cache utilization + Unit-specific details + Comprehensive data")
        print("=" * 60)
        
        start_time = time.time()
        
        # Step 1: Load and cache plant details
        print("\n💾 STEP 1: LOADING PLANT DETAILS TO CACHE")
        print("-" * 50)
        await self._load_plant_details_to_cache()
        
        # Step 2: Extract unit details using cached data
        print("\n🔧 STEP 2: EXTRACTING UNIT DETAILS FROM CACHED PLANT DATA")
        print("-" * 50)
        unit_details_list = await self._extract_unit_details_from_cache()
        
        # Step 3: Display unit details
        print("\n📊 STEP 3: DISPLAYING EXTRACTED UNIT DETAILS")
        print("-" * 50)
        self._display_unit_details(unit_details_list)
        
        # Step 4: Save unit details
        print("\n💾 STEP 4: SAVING UNIT DETAILS")
        print("-" * 50)
        await self._save_unit_details(unit_details_list)
        
        # Step 5: Show cache information
        print("\n📈 STEP 5: CACHE INFORMATION")
        print("-" * 50)
        self._display_cache_info()
        
        total_time = time.time() - start_time
        print(f"\n🎉 CACHED UNIT EXTRACTION DEMO COMPLETED!")
        print(f"⏱️  Total time: {total_time:.1f} seconds")
        print(f"🔧 Units extracted: {len(unit_details_list)}")
        
        return unit_details_list
    
    async def _load_plant_details_to_cache(self):
        """Load plant details to cache memory."""
        from src.cache_manager import plant_cache
        
        # Load the filled plant data from our previous demo
        try:
            with open("demo_filled_data_20250529_111110.json", 'r', encoding='utf-8') as f:
                plant_data = json.load(f)
            
            # Store in cache
            cache_key = plant_cache.store_plant_details(self.plant_name, plant_data)
            
            print(f"✅ Plant details loaded to cache with key: {cache_key}")
            print(f"📊 Cached fields: {len(plant_data)} plant fields")
            print(f"   • Basic info: name, type, location, coordinates")
            print(f"   • Units: {plant_data.get('units_id', [])}")
            print(f"   • Grid connections: {len(plant_data.get('grid_connectivity_maps', []))} maps")
            print(f"   • PPA details: {len(plant_data.get('ppa_details', []))} agreements")
            
        except FileNotFoundError:
            print("⚠️  Previous plant data file not found. Creating mock data...")
            # Create mock plant data
            plant_data = {
                "name": "Jhajjar Power Plant",
                "plant_type": "coal",
                "lat": "28.607111",
                "long": "76.656914",
                "plant_address": "Jharli village, Jhajjar district, Haryana, India",
                "units_id": [1, 2],
                "plant_id": 1,
                "ppa_details": [
                    {
                        "capacity": "1320 MW",
                        "capacity_unit": "MW",
                        "start_date": "2012",
                        "respondents": [
                            {
                                "name": "Haryana State Distribution Companies",
                                "capacity": "1188 MW",
                                "price": "2.89"
                            }
                        ]
                    }
                ],
                "grid_connectivity_maps": [
                    {
                        "details": [
                            {
                                "substation_name": "Sonipat Substation",
                                "capacity": "400 kV",
                                "latitude": "28.9931",
                                "longitude": "77.0151"
                            }
                        ]
                    }
                ]
            }
            
            cache_key = plant_cache.store_plant_details(self.plant_name, plant_data)
            print(f"✅ Mock plant details created and cached with key: {cache_key}")
    
    async def _extract_unit_details_from_cache(self):
        """Extract unit details using cached plant data."""
        from src.unit_details_extractor import UnitDetailsExtractor
        
        print("🔧 Initializing unit details extractor...")
        extractor = UnitDetailsExtractor()
        
        print(f"🔍 Extracting unit details for: {self.plant_name}")
        print("💾 Using cached plant data for extraction...")
        
        # Extract unit details
        unit_details_list, extraction_info = await extractor.extract_unit_details(
            self.plant_name, use_cached_plant_data=True
        )
        
        print(f"✅ Unit extraction completed")
        print(f"📊 Extraction summary:")
        print(f"   • Cached plant data used: {extraction_info.get('cached_plant_data_used', False)}")
        print(f"   • Units found: {extraction_info.get('units_found', 0)}")
        print(f"   • Units extracted: {extraction_info.get('units_extracted', 0)}")
        print(f"   • Fields from cache: {len(extraction_info.get('fields_from_cache', []))}")
        
        return unit_details_list
    
    def _display_unit_details(self, unit_details_list: List):
        """Display extracted unit details."""
        print(f"🔧 Extracted details for {len(unit_details_list)} units:")
        
        for i, unit_details in enumerate(unit_details_list, 1):
            if hasattr(unit_details, 'model_dump'):
                unit_data = unit_details.model_dump()
            else:
                unit_data = unit_details
            
            print(f"\n   📋 Unit {unit_data.get('unit_id', i)} Details:")
            print(f"      🏷️  Name: {unit_data.get('unit_name', 'N/A')}")
            print(f"      🏭 Plant: {unit_data.get('plant_name', 'N/A')}")
            
            # Display specifications
            specs = unit_data.get('specifications', {})
            if specs:
                print(f"      ⚙️  Specifications:")
                print(f"         • Capacity: {specs.get('capacity_mw', 'N/A')} MW")
                print(f"         • Technology: {specs.get('technology', 'N/A')}")
                print(f"         • Manufacturer: {specs.get('manufacturer', 'N/A')}")
                print(f"         • Fuel Type: {specs.get('fuel_type', 'N/A')}")
                print(f"         • Efficiency: {specs.get('efficiency_percent', 'N/A')}%")
            
            # Display operational data
            op_data = unit_data.get('operational_data', {})
            if op_data:
                print(f"      📊 Operational Data:")
                print(f"         • Availability Factor: {op_data.get('availability_factor', 'N/A')}%")
                print(f"         • Capacity Factor: {op_data.get('capacity_factor', 'N/A')}%")
                print(f"         • Annual Generation: {op_data.get('annual_generation_gwh', 'N/A')} GWh")
            
            # Display environmental data
            env_data = unit_data.get('environmental_data', {})
            if env_data:
                print(f"      🌱 Environmental Data:")
                print(f"         • CO2 Emissions: {env_data.get('co2_emissions_tons_per_year', 'N/A')} tons/year")
                print(f"         • Water Consumption: {env_data.get('water_consumption_m3_per_mwh', 'N/A')} m³/MWh")
                print(f"         • Cooling System: {env_data.get('cooling_system_type', 'N/A')}")
            
            # Display grid and PPA info
            grid_connection = unit_data.get('unit_grid_connection', '')
            ppa_allocation = unit_data.get('unit_ppa_allocation', '')
            if grid_connection:
                print(f"      🔌 Grid Connection: {grid_connection}")
            if ppa_allocation:
                print(f"      📄 PPA Allocation: {ppa_allocation}")
    
    async def _save_unit_details(self, unit_details_list: List):
        """Save unit details to files."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Convert to serializable format
        units_data = []
        for unit_details in unit_details_list:
            if hasattr(unit_details, 'model_dump'):
                units_data.append(unit_details.model_dump())
            else:
                units_data.append(unit_details)
        
        # Save individual unit details
        for i, unit_data in enumerate(units_data):
            unit_file = f"unit_{unit_data.get('unit_id', i+1)}_details_{timestamp}.json"
            with open(unit_file, 'w', encoding='utf-8') as f:
                json.dump(unit_data, f, indent=2, ensure_ascii=False)
            print(f"🔧 Unit {unit_data.get('unit_id', i+1)} details saved to: {unit_file}")
        
        # Save combined unit details
        combined_unit_file = f"all_units_details_{timestamp}.json"
        combined_data = {
            "plant_name": self.plant_name,
            "extraction_timestamp": datetime.now().isoformat(),
            "total_units": len(units_data),
            "units": units_data
        }
        
        with open(combined_unit_file, 'w', encoding='utf-8') as f:
            json.dump(combined_data, f, indent=2, ensure_ascii=False)
        print(f"📋 Combined unit details saved to: {combined_unit_file}")
    
    def _display_cache_info(self):
        """Display cache information."""
        from src.cache_manager import plant_cache
        
        cache_info = plant_cache.get_cache_info()
        
        print("📈 Cache Information:")
        print(f"   • Total entries: {cache_info['total_entries']}")
        print(f"   • Memory entries: {cache_info['memory_entries']}")
        print(f"   • Total size: {cache_info['total_size_mb']} MB")
        print(f"   • Cache directory: {cache_info['cache_dir']}")
        print(f"   • TTL: {cache_info['ttl_hours']} hours")
        
        if cache_info['entries']:
            print(f"   • Cached entries:")
            for entry in cache_info['entries']:
                print(f"     - {entry}")


async def main():
    """Main demo function."""
    print("🔧 CACHED UNIT EXTRACTION DEMO")
    print("This demo shows how to extract unit details using cached plant data")
    print()
    
    try:
        demo = CachedUnitExtractionDemo()
        unit_details_list = await demo.run_cached_unit_extraction_demo()
        
        print(f"\n📄 SAMPLE UNIT DETAILS JSON PREVIEW")
        print("=" * 50)
        if unit_details_list:
            sample_unit = unit_details_list[0]
            if hasattr(sample_unit, 'model_dump'):
                sample_data = sample_unit.model_dump()
            else:
                sample_data = sample_unit
            
            print(json.dumps(sample_data, indent=2, ensure_ascii=False))
        
        print(f"\n✅ CACHED UNIT EXTRACTION DEMO COMPLETED SUCCESSFULLY!")
        print("Check the generated JSON files for complete unit details.")
        
    except Exception as e:
        print(f"\n❌ DEMO FAILED: {e}")
        logger.error(f"Demo failed: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
