#!/usr/bin/env python3
"""
Quick test to verify the plant address extraction fix.
"""

import asyncio
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.simple_pipeline import SimplePowerPlantPipeline
from src.scraper_client import ScraperAPIClient
from src.models import ScrapedContent

async def test_address_extraction():
    """Test the address extraction with known content."""

    # Initialize the pipeline
    pipeline = SimplePowerPlantPipeline()

    # Create mock scraped content with the Wikipedia data we know contains the address
    from datetime import datetime
    wikipedia_content = ScrapedContent(
        url="https://en.wikipedia.org/wiki/Jhajjar_Power_Station",
        title="Jhajjar Power Station - Wikipedia",
        content="""From Wikipedia, the free encyclopedia
**Mahatma Gandhi Super Thermal Power Project** [1] is located at Jharli
village in Jhajjar district of Haryana. The coal based power project was
developed by CLP India Private Limited, a subsidiary of CLP Group.
Stage | Unit Number | Installed Capacity (MW) | Date of Commissioning | Status
---|---|---|---|---
Stage I | 1 | 660 | 2012 March | Running [2]
Stage II | 2 | 660 | 2012 July | Running [3]
The two cooling towers of the Jhajjar Power Station""",
        content_length=490,
        source_type="wikipedia",
        relevance_score=0.67,
        extraction_timestamp=datetime.now().isoformat()
    )

    # Test the address extraction directly
    print("🧪 Testing Address Extraction with Wikipedia Content")
    print("=" * 60)

    # Test the RAG extraction method
    result = await pipeline._extract_field_with_rag(
        "plant_address",
        [wikipedia_content],
        "Jhajjar Power Plant"
    )

    print(f"📍 Extracted Address: {result}")

    if result:
        print("✅ SUCCESS: Address extraction is working!")
    else:
        print("❌ FAILED: Address extraction is not working")

    print("\n🔍 Expected: Should extract something like 'Jharli Village In Jhajjar District Of Haryana'")

if __name__ == "__main__":
    asyncio.run(test_address_extraction())
